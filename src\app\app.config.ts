import {
  ApplicationConfig,
  importProvidersFrom,
  provideBrowserGlobalErrorListeners,
  provideZonelessChangeDetection,
} from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';

import { registerLocaleData } from '@angular/common';
import { HttpClient, provideHttpClient, withFetch } from '@angular/common/http';
import it from '@angular/common/locales/it';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { FlagBasedPreloadingStrategy } from '@core/services/flag-based.preloading-strategy';
import { provideTranslateService, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { it_IT, provideNzI18n } from 'ng-zorro-antd/i18n';
import { routes } from './app.routes';
import { provideNzIcons } from './icons-provider';

registerLocaleData(it);

export const appConfig: ApplicationConfig = {
  providers: [
    //TODO Attivare initializeService
    // provideAppInitializer(() => {
    //   const initializeService = inject(InitializeService);
    //   return initializeService.initConfigApp();
    // }),
    provideBrowserGlobalErrorListeners(),
    provideZonelessChangeDetection(),
    provideRouter(routes, withPreloading(FlagBasedPreloadingStrategy)),
    provideHttpClient(withFetch()),
    provideNzIcons(),
    provideNzI18n(it_IT),
    importProvidersFrom(FormsModule),
    provideAnimationsAsync(),
    provideTranslateService({
      loader: {
        provide: TranslateLoader,
        useFactory: TranslateLoaderFn,
        deps: [HttpClient],
      },
    }),
  ],
};

export function TranslateLoaderFn(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
