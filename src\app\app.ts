import { Component, inject, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { IconService } from '@core/services/utils/icon';
import { PageComponent } from '@pages/_layout/page/page.component';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, PageComponent],
  templateUrl: './app.html',
  styleUrl: './app.less',
})
export class App {
  // Inject icon service - Do not remove!
  protected iconService = inject(IconService);

  protected readonly title = signal('client');
}
