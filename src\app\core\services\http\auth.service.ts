import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { computed, inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import { roleType } from '@models/enums/role';
import { IAdmin } from '@models/interfaces/admin';
import { IAuthResponse } from '@models/interfaces/auth-response';
import { IBaseResponse } from '@models/interfaces/base-response';
import { catchError, switchMap, tap } from 'rxjs';
import { SocketService } from '../socket.service';

@Injectable({ providedIn: 'root' })
export class AuthService {
  // SERVICES
  private http = inject(HttpClient);
  private router = inject(Router);
  private location = inject(Location);
  private socketService = inject(SocketService);

  // VARIABLES
  private _baseAuthApi = `${environment.api.auth}`;
  private _baseAdminApi = `${environment.api.admin}`;
  private _isLoggedIn = signal<boolean>(false);
  readonly isLoggedIn = this._isLoggedIn.asReadonly();
  private _user = signal<IAdmin | undefined>(undefined);
  public readonly user = this._user.asReadonly();

  // COMPUTED
  public readonly isAdmin = computed(
    () => this.user()?.role === roleType.admin,
  );

  public readonly userRole = computed(() => this.user()?.role);

  setLoggedUser(user: IAdmin) {
    this._user.set(user);
  }

  login(email: string, password: string) {
    return this.http
      .post<IBaseResponse<IAuthResponse>>(`${this._baseAuthApi}/login`, {
        email,
        password,
      })
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
          localStorage.setItem(GenericUtils.user_id, res.data!.id);
          localStorage.setItem(GenericUtils.tenantID, res.data!.tenantID);
          this.setLoggedIn(true);
          this.socketService.initSocket();
        }),
        switchMap((res) => this.getStaffProfile(res.data!.id)),
      );
  }

  getStaffProfile(staffId: string) {
    return this.http
      .get<IBaseResponse<IAdmin>>(`${this._baseAdminApi}/${staffId}`)
      .pipe(tap((res) => this.setLoggedUser(res.data!)));
  }

  logout() {
    return this.http
      .get<IBaseResponse<void>>(`${this._baseAuthApi}/logout`)
      .pipe(
        tap(() => {
          this.socketService.exit().subscribe();
          localStorage.clear();
          if (this.location.path() !== '/auth/register') {
            this.router.navigateByUrl('/auth/login').then(() => {
              this.setLoggedIn(false);
            });
          }
        }),
        catchError((err) => {
          this.socketService.exit().subscribe();
          localStorage.clear();
          if (this.location.path() !== '/auth/register') {
            this.router.navigateByUrl('/auth/login').then(() => {
              this.setLoggedIn(false);
            });
          }
          console.error(err.message);
          throw err;
        }),
      );
  }

  setLoggedIn(value: boolean) {
    this._isLoggedIn.set(value);
  }

  forgotPassword(email: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/forgot-password`,
      { email },
    );
  }

  resetPassword(password: string, token: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/reset-password?token=${token}`,
      { password },
    );
  }

  activateAccount(password: string, token: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/activate-account?token=${token}`,
      { password },
    );
  }

  refreshToken() {
    return this.http
      .get<
        IBaseResponse<{ token: string }>
      >(`${this._baseAuthApi}/refresh-token`)
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
        }),
      );
  }

  resendActivationAccount(staffId: string) {
    return this.http.get<IBaseResponse<void>>(
      `${this._baseAuthApi}/${staffId}/resend-activation-account`,
    );
  }

  userHasRole(roles: roleType[]) {
    return roles.includes(this.user()?.role!);
  }
}
