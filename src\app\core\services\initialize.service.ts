/** Core */
import { Location } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { GenericUtils } from '@core/utils/generic';
import { AuthService } from './http/auth.service';
import { SocketService } from './socket.service';

const ROUTES_NO_LOGOUT = ['/auth/activate-account', '/auth/reset-password'];

@Injectable({ providedIn: 'root' })
export class InitializeService {
  // SERVICES
  private authService = inject(AuthService);
  private location = inject(Location);
  private socketService = inject(SocketService);

  initConfigApp(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const token = localStorage.getItem(GenericUtils.session_token);
      const adminId = localStorage.getItem(GenericUtils.user_id);

      if (!!token && !!adminId) {
        // GET SETTINGS AND STAFF PROFILE
        this.authService.getStaffProfile(adminId).subscribe({
          next: () => {
            this.authService.setLoggedIn(true);
            this.socketService.initSocket();
            resolve(true);
          },
          error: () => {
            this.authService.logout().subscribe({
              next: () => {
                resolve(true);
              },
            });
          },
        });
      } else {
        const currentUrl = this.location.path();

        if (ROUTES_NO_LOGOUT.find((item) => currentUrl.startsWith(item))) {
          // SKIP LOGOUT
          // resolve(true);
        } else {
          this.authService.logout().subscribe({});
        }
        resolve(true);
      }
    });
  }
}
