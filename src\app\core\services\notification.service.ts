import { HttpClient } from '@angular/common/http';
import { effect, Injectable, signal, untracked } from '@angular/core';
import { environment } from '@env/environment';
import { socketActionTypeResponse } from '@models/enums/socket';
import { IBaseResponse } from '@models/interfaces/base-response';
import { INotification } from '@models/interfaces/notification-boutique';
import { NzMessageService } from 'ng-zorro-antd/message';
import { tap } from 'rxjs';
import { SocketService } from './socket.service';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  private _notifications = signal<INotification[]>([]);
  public notifications = this._notifications.asReadonly();
  socketReadyEffect = effect(() => {
    if (this.socketService.isSocketReady()) {
      untracked(() => this.initNotificationFromSocket());
    }
  });
  private _baseNotificationApi = environment.api.notifications;

  constructor(
    private socketService: SocketService,
    private http: HttpClient,
    private message: NzMessageService,
  ) {}

  initNotificationFromSocket() {
    this.socketService.socket.on(
      socketActionTypeResponse.notifications,
      (data: INotification) => {
        this._notifications.update((prev) => [data, ...prev]);
        // Riproduci il suono solo se l'utente ha dato il consenso
        if (localStorage.getItem('notificationSoundConsent') === 'true') {
          try {
            const audio = new Audio('assets/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(() => {});
          } catch (e) {
            // Silenzia eventuali errori di creazione Audio
          }
        }
      },
    );
  }

  getNotifications() {
    return this.http
      .get<IBaseResponse<INotification[]>>(`${this._baseNotificationApi}`)
      .pipe(tap((res) => this._notifications.set(res.data)));
  }

  onRemoveNotification(id: string) {
    this.http
      .delete<IBaseResponse<void>>(`${this._baseNotificationApi}/${id}`)
      .subscribe({
        next: () => {
          this._notifications.update((prev) =>
            prev.filter((data) => id !== data.id),
          );
        },
      });
  }

  clearNotifications() {
    this.http
      .delete<IBaseResponse<void>>(`${this._baseNotificationApi}`)
      .subscribe({
        next: () => {
          this._notifications.set([]);
        },
      });
  }

  success(message: string) {
    this.message.success(message);
  }

  error(message: string) {
    this.message.error(message);
  }

  warning(message: string) {
    this.message.warning(message);
  }

  info(message: string) {
    this.message.info(message);
  }
}
