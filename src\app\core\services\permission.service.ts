import { Injectable, signal } from '@angular/core';
import { crudActionType } from '@models/enums/crud-action-type';
import { ICrudPermission } from '@models/interfaces/permission';

@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  public permissions = signal<Map<string, ICrudPermission>>(new Map());

  /** Loads permissions by list of permissions  */
  public loadPermissions(permissions: string[]) {
    this.clearPermissions();

    if (!permissions || permissions.length <= 0) return;

    permissions.forEach((act) => {
      const section = act.split(':')[0];
      const crudType = act.split(':')[1];

      let crudPermission: ICrudPermission = this.permissions().has(section)
        ? this.permissions().get(section)
        : { read: false, create: false, update: false, delete: false };

      switch (crudType) {
        case 'read':
          crudPermission.read = true;
          break;
        case 'create':
          crudPermission.create = true;
          break;
        case 'update':
          crudPermission.update = true;
          break;
        case 'delete':
          crudPermission.delete = true;
          break;
      }

      this.permissions().set(section, crudPermission);
    });
  }

  /** Gets permission by permission name */
  public getPermission(permissionName: string): ICrudPermission {
    return this.permissions().get(permissionName);
  }

  /** Gets read permission by permission name  */
  public hasReadPermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.read) return act.read;
    else return false;
  }

  /** Gets create permission by permission name  */
  public hasCreatePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.create) return act.create;
    else return false;
  }

  /** Gets update permission by permission name  */
  public hasUpdatePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.update) return act.update;
    else return false;
  }

  /** Gets delete permission by permission name  */
  public hasDeletePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.delete) return act.delete;
    else return false;
  }

  public searchPermission(section: string, crudType: crudActionType) {
    switch (crudType) {
      case crudActionType.create:
        return this.hasCreatePermission(section);
      case crudActionType.read:
        return this.hasReadPermission(section);
      case crudActionType.update:
        return this.hasUpdatePermission(section);
      case crudActionType.delete:
        return this.hasDeletePermission(section);

      default:
        return null;
    }
  }

  private clearPermissions() {
    this.permissions().clear();
  }
}
