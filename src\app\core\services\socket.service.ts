import { Injectable, signal } from "@angular/core";
import { GenericUtils } from "@core/utils/generic";
import { log } from "@core/utils/logger";
import { environment } from "@env/environment";
import {
  socketActionType,
  socketActionTypeResponse,
} from "@models/enums/socket";
import { Observable } from "rxjs";
import { io, Socket } from "socket.io-client";

@Injectable({
  providedIn: "root",
})
export class SocketService {
  public socket: Socket;
  private _isSocketReady = signal<boolean>(false);
  public isSocketReady = this._isSocketReady.asReadonly();

  private _bookingsNeedUpdate = signal<number>(0);
  public bookingsNeedUpdate = this._bookingsNeedUpdate.asReadonly();

  private _dashboardNeedUpdate = signal<number>(0);
  public dashboardNeedUpdate = this._dashboardNeedUpdate.asReadonly();

  constructor() {}

  connect() {
    return new Observable((observer) => {
      this.socket.emit(socketActionType.connection);
      observer.next(null);
    });
  }

  exit() {
    log("exit socket...");
    return new Observable((observer) => {
      this.socket?.emit(socketActionType.exit);
      observer.next(null);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  initSocket() {
    // Check if socket is already initialized
    this.disconnect();

    this.socket = io(environment.api.base, {
      auth: {
        token: localStorage.getItem(GenericUtils.session_token),
      },
    });

    this.socket.on(socketActionTypeResponse.connected, () => {
      log("socket connected...");
      this._isSocketReady.set(true);
    });

    this.socket.on(socketActionTypeResponse.exited, () => {
      log("socket disconnected", this.socket);
      this.disconnect();
    });

    this.socket.on(socketActionTypeResponse.updatedBookings, () => {
      this._bookingsNeedUpdate.update((prev) => prev + 1);
    });

    this.socket.on(socketActionTypeResponse.updatedStaffSettings, () => {
      this._bookingsNeedUpdate.update((prev) => prev + 1);
    });

    this.socket.on(socketActionTypeResponse.updatdeDashboard, () => {
      this._dashboardNeedUpdate.update((prev) => prev + 1);
    });
  }
}
