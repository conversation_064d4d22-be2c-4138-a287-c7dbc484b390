import { inject } from "@angular/core";
import { AbstractControl, FormArray, FormGroup } from "@angular/forms";
import { CanDeactivateFn } from "@angular/router";
import { ModalService } from "./modal";

export const canDeactivateFormGuard = (
  getFormFn: (component: any) => FormGroup
): CanDeactivateFn<any> => {
  return (component: any) => {
    const modalService = inject(ModalService);
    const form = getFormFn(component);

    const hasModifiedFormArray = Object.values(form.controls).some(
      (control: AbstractControl) =>
        control instanceof FormArray && control.dirty
    );

    if (form.dirty || hasModifiedFormArray) {
      return modalService.confirmDeleteCanDeactivate(
        "canDeactivateTitle",
        "canDeactivateSubtitle"
      );
    }

    return true;
  };
};
