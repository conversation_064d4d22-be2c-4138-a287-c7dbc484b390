import { Injectable, signal, TemplateRef } from "@angular/core";
import { currentSectionType } from "@models/enums/current-section";

@Injectable({
  providedIn: "root",
})
export class HeaderService {
  protected _data = signal<currentSectionType | undefined>(undefined);
  public data$ = this._data.asReadonly();
  public template = signal<TemplateRef<any> | undefined>(undefined);

  setCurrentSection(data: currentSectionType, template?: TemplateRef<any>) {
    this._data.set(data);
    this.template.set(template);
  }

  constructor() {}
}
