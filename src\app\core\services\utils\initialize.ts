import { APP_INITIALIZER } from "@angular/core";
import { environment } from "@env/environment";
import { LanguageService } from "./language";
import { ThemeService } from "./theme";

export const initializeProvider = {
  provide: APP_INITIALIZER,
  useFactory:
    (themeService: ThemeService, languageService: LanguageService) => () => {
      languageService.configureLang(environment.languages);
      return Promise.all([
        themeService.loadTheme(),
        languageService.loadLanguage(),
      ]);
    },
  deps: [ThemeService, LanguageService],
  multi: true,
};
