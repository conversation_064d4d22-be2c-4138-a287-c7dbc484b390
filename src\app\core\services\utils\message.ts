import { Injectable, signal, TemplateRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';

@Injectable({
  providedIn: 'root'
})
export class MessageService {
  private _isLoading = signal<boolean>(false);
  public readonly isLoading = this._isLoading.asReadonly();

  constructor(
    private nzMessageService: NzMessageService,
    private translateService: TranslateService
  ) {
  }

  setIsLoading(isLoading: boolean) {
    this._isLoading.set(isLoading);
  }

  addMessage(
    type: 'success' | 'info' | 'warning' | 'error' | 'loading' | string,
    content: string | TemplateRef<void>,
    clearPreviousMessages: boolean,
    loadingState?: boolean
  ): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    if (typeof content === 'string')
      content = this.translateService.instant(content);

    loadingState
      ? this.nzMessageService.loading(content, { nzDuration: 0 })
      : this.nzMessageService.create(type, content, { nzDuration: 3000 });
  }

  addLoadingMessage(label?: string, clearPreviousMessages = true): void {
    this.setIsLoading(true);
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.addMessage(
      'loading',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true,
      true
    );
  }

  addErrorMessage(label?: string, clearPreviousMessages = true): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.setIsLoading(false);
    this.addMessage(
      'error',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true
    );
  }

  addSuccessMessage(label?: string, clearPreviousMessages = true): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.setIsLoading(false);

    this.addMessage(
      'success',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true
    );
  }
}
