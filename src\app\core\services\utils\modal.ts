import { Injectable, TemplateRef, Type } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { ModalOptions, NzModalRef, NzModalService } from "ng-zorro-antd/modal";

@Injectable({
  providedIn: "root",
})
export class ModalService {
  private nzModalRef: NzModalRef;

  constructor(
    private nzModalService: NzModalService,
    private translateService: TranslateService
  ) {}

  infoModal(
    confirmFn: Function,
    title: string,
    subtitle: string,
    cancelFn?: Function,
    closable?: boolean,
    showCancelButton?: boolean
  ): void {
    const modalTitle = title || "";
    const modalSubtitle = subtitle || "";
    const _closable = closable ?? true;
    this.nzModalService.info({
      nzTitle: this.translateService.instant(modalTitle),
      nzContent: this.translateService.instant(modalSubtitle),
      nzCancelText: showCancelButton
        ? this.translateService.instant("abort")
        : null,
      nzCancelDisabled: !showCancelButton,
      nzOnOk: () => {
        confirmFn != undefined ? confirmFn() : "";
      },
      nzOnCancel: () => {
        cancelFn != undefined ? cancelFn() : "";
      },
      nzClosable: _closable,
      nzMaskClosable: _closable,
    });
  }

  customModal(
    confirmFn: Function,
    title: string | TemplateRef<any>,
    content: TemplateRef<any> | Type<any>,
    footer?: TemplateRef<any>,
    cancelFn?: Function,
    width?: string | number,
    closable?: boolean,
    maskClosable?: boolean,
    fullScreen?: boolean,
    className?: string
  ): void {
    const modalTitle = title || "";
    const _content = content || "<div></div>";
    const _footer = footer || null;
    const _closable = closable ?? true;
    const _maskClosable = maskClosable ?? true;

    const modalConfig = {
      nzTitle:
        typeof title === "string"
          ? this.translateService.instant(modalTitle as string)
          : title,
      nzContent: _content,
      nzWidth: fullScreen ? "100vw" : width || 520,
      nzOnOk: () => {
        confirmFn != undefined ? confirmFn() : "";
      },
      nzOnCancel: () => {
        cancelFn != undefined ? cancelFn() : "";
      },
      nzClosable: _closable,
      nzMaskClosable: _maskClosable,
      ...(fullScreen && {
        nzWrapClassName: "full-screen-modal",
        nzStyle: { width: "100vw", height: "100vh", top: 0, padding: 0 },
      }),
      ...(className && { nzClassName: className }),
    };

    this.nzModalRef = this.nzModalService.create({
      ...modalConfig,
      ...(footer ? { nzFooter: _footer } : { nzFooter: null }),
    });
  }

  updateModal(config: Partial<ModalOptions>) {
    if (!this.nzModalRef?.containerInstance) return;
    // Translate nzTitle if exists
    if (config.nzTitle) {
      config = {
        ...config,
        nzTitle: config.nzTitle,
      };
    }
    const prevConfig = this.nzModalRef.containerInstance.config;
    // Update config
    this.nzModalRef?.updateConfig({ ...prevConfig, ...config });
    this.nzModalRef.containerInstance.cdr.markForCheck();
  }

  closeModal() {
    this.nzModalService.closeAll();
  }

  confirmDelete(
    confirmFn: Function,
    title: string,
    subtitle: string,
    cancelFn?: Function
  ): void {
    this.nzModalService.error({
      nzTitle: this.translateService.instant(title),
      nzContent: this.translateService.instant(subtitle),
      nzCancelText: this.translateService.instant("MODAL.abort"),
      nzOkDanger: true,
      nzOnOk: () => {
        confirmFn != undefined ? confirmFn() : "";
      },
      nzOnCancel: () => {
        cancelFn != undefined ? cancelFn() : "";
      },
    });
  }

  confirmDeleteCanDeactivate(
    title: string,
    subtitle: string
  ): Promise<boolean> {
    return new Promise((resolve) => {
      this.nzModalService.warning({
        nzTitle: this.translateService.instant(title),
        nzContent: this.translateService.instant(subtitle),
        nzCancelText: this.translateService.instant("MODAL.abort"),
        nzOkText: this.translateService.instant("MODAL.yes"),
        nzOkDanger: true,
        nzClosable: false,
        nzOnOk: () => resolve(true),
        nzOnCancel: () => resolve(false),
      });
    });
  }
}
