import { Injectable } from '@angular/core';
import { CheckUtils } from '@core/utils/check';
import { themeType } from '@models/enums/theme';
import { Subject } from 'rxjs';

const _theme = '_theme';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  currentTheme = themeType.light;
  oldTheme = themeType.light;
  theme$ = new Subject<themeType>();

  constructor() {
    let theme: themeType = localStorage.getItem(_theme) as themeType;
    if (!CheckUtils.isNullUndefinedOrEmpty(theme)) this.currentTheme = theme;
  }

  /**
   * Remove the theme from the DOM and remove the theme's style from the head.
   * @param {themeType} theme - The name of the theme to be removed.
   */
  #removeUnusedTheme(theme: themeType): void {
    document.documentElement.classList.remove(theme);
    const removedThemeStyle = document.getElementById(theme);
    if (removedThemeStyle) {
      document.head.removeChild(removedThemeStyle);
    }
  }

  /**
   * Load a CSS file.
   * @param {string} href - The URL of the CSS file to load.
   * @param {string} id - The id of the element that will be loaded.
   * @returns The Promise object is returned.
   */
  #loadCss(href: string, id: string): Promise<Event> {
    return new Promise((resolve, reject) => {
      const style = document.createElement('link');
      style.rel = 'stylesheet';
      style.href = href;
      style.id = id;
      style.onload = resolve;
      style.onerror = reject;
      document.head.append(style);
    });
  }

  /**
   * Loads the theme and sets the cookie.
   * @param [firstLoad=true] - boolean
   * @returns The promise is returned and the event is resolved.
   */
  public loadTheme(firstLoad = true): Promise<Event> {
    const theme = this.currentTheme;

    this.#sendTheme();

    if (firstLoad) {
      document.documentElement.classList.add(theme);
    }
    return new Promise<Event>((resolve, reject) => {
      this.#loadCss(`${theme}.css`, theme).then(
        (e) => {
          if (!firstLoad) {
            document.documentElement.classList.add(theme);
          }
          if (this.oldTheme !== this.currentTheme)
            this.#removeUnusedTheme(this.oldTheme);
          resolve(e);
        },
        (e) => reject(e),
      );
    });
  }

  /**
   * Toggle the theme between light and dark.
   * @param {themeType} [theme] - The theme to be loaded. If not specified, the current theme will be
   * used.
   * @returns The promise of the loadTheme() method.
   */
  toggleTheme(theme: themeType): Promise<Event> {
    this.oldTheme = this.currentTheme;
    if (theme) this.currentTheme = theme;
    return this.loadTheme(false);
  }

  /**
   * Send the current theme to the browser.
   */
  #sendTheme() {
    this.theme$.next(this.currentTheme);
    localStorage.setItem(_theme, this.currentTheme);
  }
}
