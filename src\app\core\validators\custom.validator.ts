import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from "@angular/forms";
import { map, Observable, switchMap, timer } from "rxjs";

export class CustomValidators {
  public static confirmPasswordValidator(
    control1: string,
    control2: string
  ): ValidatorFn {
    return (form: FormGroup): ValidationErrors | null => {
      if (form.get(control1)!.value === form.get(control2)!.value) {
        return null;
      } else {
        form.get(control2)!.setErrors({ passwordMismatch: true });
        return {
          passwordMismatch: true,
        };
      }
    };
  }

  public static dateValidator(): ValidatorFn {
    return (control: AbstractControl) => {
      const startDate = new Date(1900, 0, 1);
      const value = new Date(control.value);
      const differenceMs = value.getTime() - startDate.getTime();

      if (differenceMs >= 0) {
        return null;
      } else return { DateNotValid: true };
    };
  }

  public static timeRangeValidator(
    startHour: string,
    endHour: string
  ): ValidatorFn {
    return (controls: AbstractControl) => {
      const startHourValue = controls.get(startHour).value;
      const endHourValue = controls.get(endHour).value;

      let dateA = new Date(startHourValue);
      let dateB = new Date(endHourValue);

      if (!dateA && !dateB) return { dateError: true };

      if (dateB > dateA) {
        controls.get(endHour).setErrors(null);
        return null;
      } else {
        controls.get(endHour).setErrors({ dateError: true });
        return { dateError: true };
      }
    };
  }

  public static nonOverlappingIntervalsValidator(
    formArray: AbstractControl
  ): ValidationErrors | null {
    // Prima di tutto, controlliamo se è davvero un FormArray
    if (!(formArray instanceof FormArray)) {
      return null;
    }

    // Se non ho almeno 2 fasce, niente da validare
    if (formArray.length < 2) {
      return null;
    }

    // Ricavo i due FormGroup (assumendo di averne esattamente 2)
    const firstTimeSlot = formArray.at(0) as FormGroup;
    const secondTimeSlot = formArray.at(1) as FormGroup;

    // Estraggo i valori start e end
    const firstEnd = firstTimeSlot.get("end")?.value; // Date
    const secondStart = secondTimeSlot.get("start")?.value; // Date

    // Se il secondo inizia prima (o uguale) di quando finisce il primo => ERRORE
    if (firstEnd && secondStart && secondStart <= firstEnd) {
      // Ritorno un oggetto che rappresenta l'errore
      return { overlappingError: true };
    }
    // Altrimenti va tutto bene
    return null;
  }

  public static validateInputAsync(
    requestHttp: (value: string) => Observable<any>,
    errorName: string
  ): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return timer(500).pipe(
        switchMap(() =>
          requestHttp(control.value).pipe(
            map((res) => (res.data?.available ? null : { [errorName]: true }))
          )
        )
      );
    };
  }

  public static emailRegex =
    "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

  public static anyInterNumberRegex = "^-?[0-9]\\d*(\\.\\d{1,2})?$";

  public static positiveInterNumberRegex = "/^[0-9]d*$/";

  public static subdomainRegex = "^[a-zA-Z0-9-]+$";
}
