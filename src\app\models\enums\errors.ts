export enum errorType {
  GENERIC = 'GENERIC',
  E1001 = 'emailOrPasswordWrong',
  E1002 = 'emailWrong',
  E1004 = 'tokenExpired',
  E1005 = 'tokenMissing',
  E1007 = 'userNotFound',
  E1009 = 'userNotActive',
  E1010 = 'userAlreadyExist',
  E1011 = 'onboardingError',
  E1012 = 'userAlreadyActive',
  E1013 = 'operationNotAllowed',
  E1014 = 'googleCalendarError',
  E1015 = 'dateError',
  E1016 = 'salonNotFound',
  E1017 = 'resourceNotFound',
  E1018 = 'paymentError',
  E1019 = 'missingQueryParameters',
  E1020 = 'bookingCompleted',
  E1021 = 'bookingPast',
  E1022 = 'slotUnavailable',
  E1023 = 'invalidIdFormat',
  E1025 = 'staffDeletedCheckBookings',
  E1031 = 'dailyOpenHoursNotFound',
  E1033 = 'timeslotErrorWithDailyOpenHours',
  E1034 = 'openDayConflictWithClosedDays',
  E1035 = 'openDayConflictWithWorkingHours',
  E1036 = 'salonDailyOpenHoursNotFound',
  E1037 = 'timeSlotErrorWithSalonDailyOpenHours',
}
