import { Directive, Input } from '@angular/core';
import { NzDrawerPlacement } from 'ng-zorro-antd/drawer';

@Directive({
  selector: '[drawerMask]',
  standalone: true
})
export class drawerMaskDirective {
  /**Input Draawer Mask Flag
   *
   * Default false
   */
  @Input() mask: boolean = false;
  /** Input drawer position */
  @Input() drawerPosition!: NzDrawerPlacement;
  /**Input flag when drawer is visible */
  @Input() drawerVisibile: boolean = true;

  ngOnChanges(): void {
    if (!this.mask && this.drawerVisibile) {
      var drawerCollection = document.getElementsByClassName(
        'ant-drawer-' + this.drawerPosition
      );
      if (drawerCollection.length > 0)
        switch (this.drawerPosition) {
          case 'left':
          case 'right':
            drawerCollection[0].setAttribute('style', 'width: 0');
            break;
          case 'top':
          case 'bottom':
            drawerCollection[0].setAttribute('style', 'height: 0');
            break;
        }
    }
  }
}
