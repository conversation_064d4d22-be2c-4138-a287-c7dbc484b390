// import { ICompany } from "./onboarding";

// export interface ISalonSettings {
//   id: string;
//   _id?: string;
//   __v?: number;
//   dailyOpenHours: IDailyOpenHours[]; // 0 (domenica) - 6 (sabato)
//   closedDays: IClosedDay[]; // es. "09:00"
//   openDays: IOpenDay[];
//   company: ICompany;
// }

// export interface IDailyOpenHours {
//   dayOfWeek: dayType;
//   timeSlots: ITimeSlot[];
// }

// export interface IClosedDay {
//   id: string;
//   date: string; // YYYY-MM-DD;
//   reason: string;
//   allDay: boolean;
//   timeSlots: ITimeSlot[];
//   blocked?: boolean;
// }

// export interface IOpenDay {
//   id: string;
//   date: string; // YYYY-MM-DD;
//   reason: string;
//   allDay: boolean;
//   timeSlots: ITimeSlot[];
// }

// export interface ITimeSlot {
//   id?: string;
//   timeSlotId?: string;
//   start: string; // `${string}:${string}`;
//   end: string; //`${string}:${string}`;
//   type?: timeSlotType;
//   blocked?: boolean;
// }

// export interface IEventResponse {
//   date: string;
//   endTime: string;
//   startTime: string;
//   id: string;
//   type: timeSlotType;
//   timeSlotId: string;
//   blocked?: boolean;
// }

// export enum timeSlotType {
//   WORKING_HOURS = "workingHours",
//   CLOSED_DAYS = "closedDays",
//   BLOCKED = "blocked",
// }

// export enum dayType {
//   SUNDAY = 0,
//   MONDAY = 1,
//   TUESDAY = 2,
//   WEDNESDAY = 3,
//   THURSDAY = 4,
//   FRIDAY = 5,
//   SATURDAY = 6,
// }
