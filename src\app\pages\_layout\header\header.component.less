@import 'mixin';

.themeMixin({

  :host {
    .header {
      position: sticky;
      top: 0;
      background: @layout-main-page;
      z-index: 999;

      .breadcrumb {
        color: @text-color-secondary;

        &:hover {
          color: @primary-color;
        }

        &.disabled {
          cursor: context-menu;

          &:hover {
            color: @text-color-secondary;
          }
        }
      }

      .ant-page-header {
        padding: 0;
        padding-top: 16px;
        margin-bottom: 16px;
      }


      .divider {
        margin: 0;
        margin-bottom: 16px;
      }
    }
  }

});