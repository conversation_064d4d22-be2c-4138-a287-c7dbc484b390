<div nz-row class="top-bar-desktop">
  <div nz-col nzSpan="24" class="top-bar-column">
    <!-- ***** Theme menu *****-->
    <nz-switch
      class="switch-top-bar"
      [ngModel]="currentTheme == themeType.dark"
      [nzCheckedChildren]="checkedTemplate"
      [nzUnCheckedChildren]="unCheckedTemplate"
      (ngModelChange)="
        changeTheme($event == true ? themeType.dark : themeType.light)
      "
      data-cy="theme-switch"
    >
    </nz-switch>
    <ng-template #unCheckedTemplate
      ><i nz-icon nzType="biuti-ui:light"></i
    ></ng-template>
    <ng-template #checkedTemplate
      ><i nz-icon nzType="biuti-ui:dark"></i
    ></ng-template>
    <!-- ***** Language menu *****-->
    @if (languages) {
      <a
        nz-dropdown
        [nzDropdownMenu]="menu_language"
        [nzPlacement]="'bottomCenter'"
        class="btn-top-bar btn-layout"
      >
        <i nz-icon nzType="global"></i>
      </a>
    }

    <nz-dropdown-menu #menu_language="nzDropdownMenu">
      <ul nz-menu>
        @for (language of languages; track language.code) {
          @if (language.enabled) {
            <li
              [nzSelected]="language.code == currentLanguage"
              nz-menu-item
              (click)="changeLanguage(language)"
            >
              <i nz-icon nzType="biuti-ui:flag-{{ language.code }}"></i
              ><span class="ml-8">{{ language.name }}</span>
            </li>
          }
        }
      </ul>
    </nz-dropdown-menu>

    <a
      nz-dropdown
      [nzDropdownMenu]="notification_menu"
      [nzPlacement]="'bottomCenter'"
      class="btn-top-bar btn-layout"
    >
      <nz-badge [nzCount]="notifications()?.length || null" [nzSize]="'small'">
        <i nz-icon nzType="biuti-ui:bell" class="icon h-100"></i>
      </nz-badge>
    </a>

    <nz-dropdown-menu #notification_menu="nzDropdownMenu">
      <ul nz-menu>
        <ng-template #tplListHeader>
          <div class="list-header">
            <h3>{{ "NOTIFICATIONS.notifications" | translate }}</h3>
            <app-simple-button
              [title]="
                isSoundEnabled() ? 'SOUNDS.disableSound' : 'SOUNDS.enableSound'
              "
              (onButtonClick)="changeSoundPreference()"
              [icon]="isSoundEnabled() ? 'muted' : 'sound'"
              [autoMinify]="false"
              [type]="'default'"
              [size]="'small'"
            ></app-simple-button>
          </div>
        </ng-template>
        <nz-list
          [nzFooter]="notifications().length > 0 ? tplListFooter : null"
          [nzHeader]="tplListHeader"
        >
          @for (item of notifications(); track item.id) {
            <nz-list-item>
              <nz-list-item-meta>
                <nz-list-item-meta-title>
                  <nz-badge
                    [nzStatus]="
                      item.event !== 'CANCELLED' ? 'success' : 'error'
                    "
                  ></nz-badge>
                  <p style="display: inline-block">
                    {{ item.customer }}
                    {{ "NOTIFICATIONS." + item.event | translate }}
                  </p>
                </nz-list-item-meta-title>
                <nz-list-item-meta-description>
                  <small
                    >Staff: {{ item.staff.name }} {{ item.staff.surname }}
                  </small></nz-list-item-meta-description
                >
                <nz-list-item-meta-description>
                  <small>{{
                    item.bookingDate
                  }}</small></nz-list-item-meta-description
                >
              </nz-list-item-meta>

              <ul nz-list-item-actions>
                <nz-list-item-action>
                  <button
                    nz-button
                    nzType="link"
                    nzShape="circle"
                    nzSize="small"
                    nzDanger
                    (click)="onRemoveNotification(item.id)"
                  >
                    <span nz-icon nzType="close"></span>
                  </button>
                </nz-list-item-action>
              </ul>
            </nz-list-item>
          } @empty {
            <nz-list-empty
              style="min-width: 150px"
              [nzNoResult]="notFoundTpl"
            ></nz-list-empty>
            <ng-template #notFoundTpl>
              <nz-empty
                nzNotFoundImage="simple"
                [nzNotFoundContent]="''"
              ></nz-empty>
            </ng-template>
          }
        </nz-list>
        <ng-template #tplListFooter>
          <app-simple-button
            [title]="'deleteAll'"
            (onButtonClick)="clearNotifications()"
            [icon]="'delete'"
            [autoMinify]="false"
            [style]="{
              width: '100%',
              background: '#a61d24',
              borderColor: '#a61d24',
            }"
          ></app-simple-button>
        </ng-template>
      </ul>
    </nz-dropdown-menu>

    <!-- ***** User menu *****-->
    <a
      nz-dropdown
      [nzDropdownMenu]="menu_user"
      [nzPlacement]="'bottomRight'"
      class="btn-layout avatar-top-bar"
    >
      <nz-avatar
        [nzText]="userAvatarName() ? (userAvatarName() | uppercase) : null"
        [nzSize]="'small'"
        class="user-avatar"
      >
      </nz-avatar>
      <!-- <nz-avatar
        [nzIcon]="'biuti-ui:user'"
        [nzSize]="'small'"
        class="user-avatar"
      >
      </nz-avatar> -->
    </a>
    <nz-dropdown-menu #menu_user="nzDropdownMenu">
      <ul nz-menu>
        <!-- <ng-container>
          <li nz-menu-item (click)="onSettingsClick()">
            <i nz-icon nzType="control" nzTheme="outline"></i>
            <span class="ml-8">{{ 'TOP_BAR.preferences' | translate }}</span>
          </li>
          <li nz-menu-divider></li>
        </ng-container> -->

        <li nz-menu-item (click)="onProfileClick()">
          <i nz-icon nzType="user" nzTheme="outline"></i>
          <span class="ml-8">{{ "profile" | translate }}</span>
        </li>

        <li nz-menu-item>
          <i nz-icon nzType="export" nzTheme="outline"></i>
          <span class="ml-8">{{ "TOP_BAR.client-app" | translate }}</span>
        </li>

        <li nz-menu-item (click)="onLogoutClick()">
          <i nz-icon nzType="logout" nzTheme="outline"></i>
          <span class="ml-8">
            {{ "TOP_BAR.logout" | translate }}
          </span>
        </li>
      </ul>
    </nz-dropdown-menu>
  </div>
</div>

<div nz-row class="top-bar-mobile">
  <div
    nz-col
    nzSpan="3"
    class="top-bar-column notification-mobile"
    (click)="onMobileDrawerOpen()"
  >
    <div nz-icon nzType="biuti-ui:hamburger"></div>
  </div>

  <nz-drawer
    [nzClosable]="false"
    [nzVisible]="mobileDrawerVisible()"
    [nzPlacement]="'left'"
    [nzTitle]="tplDrawerTitle"
    (nzOnClose)="onMobileDrawerClose()"
  >
    <ng-container *nzDrawerContent>
      <ul nz-menu class="main-menu" nzTheme="light" [nzMode]="'inline'">
        <ng-container
          *ngTemplateOutlet="menuTpl; context: { menus: menus }"
        ></ng-container>
        <ng-template #menuTpl let-menus="menus">
          @for (menu of menus; track menu.id) {
            @if (menu.divider) {
              <nz-divider
                style="margin: 0"
                [nzText]="!isCollapsed ? (menu.divider.text | translate) : null"
                [nzOrientation]="'right'"
              />
            }

            @if (!menu.children) {
              <li
                nz-menu-item
                class="menu-item"
                [nzSelected]="menu.selected"
                [nzMatchRouterExact]="true"
                [nzDisabled]="menu.disabled"
                nz-tooltip
                [nzTooltipTitle]="
                  isCollapsed && menu.level == 0
                    ? (menu.title | translate)
                    : null
                "
                nzTooltipPlacement="right"
                (click)="!menu.disabled ? onMenuClick(menu) : null"
              >
                @if (menu.icon) {
                  <i nz-icon [nzType]="menu.icon"></i>
                }

                <span
                  [style]="
                    !isCollapsed && menu.level != 0 ? 'margin-left: 2.6rem' : ''
                  "
                  >{{ menu.title | translate }}</span
                >
              </li>
            } @else {
              <li
                class="menu-item"
                nz-submenu
                [nzOpen]="menu.open"
                [nzTitle]="menu.title | translate"
                [nzIcon]="menu.icon"
                [nzDisabled]="menu.disabled"
              >
                <ng-container
                  *ngTemplateOutlet="
                    menuTpl;
                    context: {
                      menus: menu.children,
                    }
                  "
                ></ng-container>
              </li>
            }
          }
        </ng-template>
      </ul>
    </ng-container>

    <ng-template #tplDrawerTitle>
      <div class="drawer-title">
        <span>Menu</span>
        <div nz-icon nzType="close" (click)="onMobileDrawerClose()"></div>
      </div>
    </ng-template>
  </nz-drawer>
  <div nz-col nzSpan="17" class="divider"></div>

  <div nz-col nzSpan="1" class="divider">
    <nz-divider [nzType]="'vertical'"></nz-divider>
  </div>

  <div nz-col nzSpan="3" class="notification-mobile">
    <div nz-icon nzType="bell"></div>
  </div>
</div>

<!-- ALTERNATIVE TOP BAR MOBILE -->
<!-- <div nz-row class="top-bar-mobile">
  <div nz-col nzSpan="20" class="top-bar-column">
    <ul nz-menu nzMode="horizontal">
      @for (menu of menus; track menu.id) {
      
        <li
          nz-menu-item
          nzSelected
          [nzSelected]="menu.selected"
          [nzMatchRouterExact]="true"
          (click)="!menu.disabled && !menu.children ? onMenuClick(menu) : null"
        >
          <div nz-icon [nzType]="menu.icon"></div>
        </li>
      }
    </ul>
  </div>
  <div nz-col nzSpan="1" class="divider">
    <nz-divider [nzType]="'vertical'"></nz-divider>
  </div>

  <div nz-col nzSpan="3" class="notification-mobile">
    <div nz-icon nzType="bell"></div>
  </div>
</div> -->
