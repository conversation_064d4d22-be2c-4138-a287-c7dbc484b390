import { NgStyle } from "@angular/common";
import { Component, Input, input, output, TemplateRef } from "@angular/core";
import { NzButtonComponent, NzButtonType } from "ng-zorro-antd/button";
import { NzIconDirective } from "ng-zorro-antd/icon";
import { NzPopoverDirective } from "ng-zorro-antd/popover";

@Component({
  selector: "app-popover-button",
  standalone: true,
  imports: [NzButtonComponent, NzPopoverDirective, NzIconDirective, NgStyle],
  templateUrl: "./popover-button.component.html",
  styleUrl: "./popover-button.component.less",
})
export class PopoverButtonComponent {
  tplTitle = input<string | TemplateRef<any>>();
  tplContent = input<string | TemplateRef<any>>();
  icon = input<string>();
  iconOnly = input<boolean>();
  visible = input<boolean>();
  type = input<NzButtonType>("default");
  @Input() style: { [key: string]: any };
  @Input() popoverPlacement:
    | "top"
    | "left"
    | "right"
    | "bottom"
    | "topLeft"
    | "topRight"
    | "bottomLeft"
    | "bottomRight"
    | "leftTop"
    | "leftBottom"
    | "rightTop"
    | "rightBottom" = "top";

  onButtonClick = output();
  visibleChange = output();
}
