import { Component, EventEmitter, Input, Output } from "@angular/core";
import { TranslateModule } from "@ngx-translate/core";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzIconModule } from "ng-zorro-antd/icon";

@Component({
  selector: "app-stripe-button",
  standalone: true,
  imports: [NzButtonModule, NzIconModule, TranslateModule],
  templateUrl: "./stripe-button.component.html",
  styleUrl: "./stripe-button.component.less",
})
export class StripeButtonComponent {
  @Input() style: { [key: string]: any };
  @Input() variant: "pay" | "manage" = "pay";
  @Input() text: string = "SUBSCRIPTION.payWith";
  @Output() onClick = new EventEmitter<void>();

  handleClick() {
    this.onClick.emit();
  }
}
