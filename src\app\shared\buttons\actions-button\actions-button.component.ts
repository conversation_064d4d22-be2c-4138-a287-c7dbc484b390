import { NgClass } from "@angular/common";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  input,
  OnInit,
  signal,
  untracked,
} from "@angular/core";
import { DestroyService } from "@core/services/utils/destroy";
import { ResizeService } from "@core/services/utils/resize";
import { CheckUtils } from "@core/utils/check";
import { TranslateModule } from "@ngx-translate/core";
import { IsSingleRowActionVisiblePipe } from "@shared/table/pipes/is-single-row-action-visible.pipe";
import { ITableRowAction } from "@shared/table/types/table.action";
import { NzButtonComponent, NzButtonType } from "ng-zorro-antd/button";
import {
  NzDropDownDirective,
  NzDropdownMenuComponent,
} from "ng-zorro-antd/dropdown";
import { NzIconDirective } from "ng-zorro-antd/icon";
import {
  NzMenuDirective,
  NzMenuItemComponent,
  NzSubMenuComponent,
} from "ng-zorro-antd/menu";
import { takeUntil } from "rxjs";

@Component({
  selector: "app-actions-button",
  standalone: true,
  imports: [
    NzDropDownDirective,
    NzDropdownMenuComponent,
    NzButtonComponent,
    NgClass,
    TranslateModule,
    NzIconDirective,
    NzMenuItemComponent,
    NzSubMenuComponent,
    NzMenuDirective,
    IsSingleRowActionVisiblePipe,
  ],
  providers: [DestroyService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: "./actions-button.component.html",
  styleUrl: "./actions-button.component.less",
})
export class ActionsButtonComponent implements OnInit {
  /** Input button actions */
  actions = input.required<ITableRowAction[]>();
  /** Input Button breakpoint for minified behaviour */
  breakpoint = input<number>(1200);
  /** Input Button Title */
  title = input<string>("ACTIONS.actions");
  /** Input Button Icon */
  icon = input<string>("unordered-list");
  /** Input Button Type (ngZorro Type) */
  type = input<NzButtonType>("default");
  /** Input Flag to enable minified behaviour (default true) */
  minified = input<boolean>(true);

  /** Input Button Dropdown placement */
  dropdownPlacement = input<
    | "bottomLeft"
    | "bottomCenter"
    | "bottomRight"
    | "topLeft"
    | "topCenter"
    | "topRight"
  >("bottomCenter");

  public _minified = signal<boolean>(false);
  public disabled = signal<boolean>(false);

  protected actionsEffect = effect(() => {
    if (!!this.actions()) {
      untracked(() => this.checkActionVisibility());
    }
  });

  constructor(
    private resizeService: ResizeService,
    private cdr: ChangeDetectorRef,
    private destroy$: DestroyService
  ) {
    this.resizeService
      .subscribe()
      .pipe(takeUntil(this.destroy$))
      .subscribe((screen) => {
        this.checkIfMinified();
        this.cdr.markForCheck();
      });
  }

  private checkIfMinified() {
    if (this.minified)
      this._minified.set(window.innerWidth < this.breakpoint());
  }

  ngOnInit(): void {
    this.checkIfMinified();
  }

  checkActionVisibility() {
    let disabled = true;
    this.actions().forEach((act) => {
      if (act.visibilityFn && act.visibilityFn() == true) {
        if (!CheckUtils.isNullUndefinedOrEmpty(act.disabled)) {
          if (act.disabled === false) disabled = false;
        } else {
          disabled = false;
        }
      }
    });
    this.disabled.set(disabled);
  }

  executeActionFn(action: ITableRowAction) {
    action.callbackFn();
  }
}
