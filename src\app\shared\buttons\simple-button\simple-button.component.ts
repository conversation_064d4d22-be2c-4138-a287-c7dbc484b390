import { <PERSON><PERSON><PERSON>, Ng<PERSON>ty<PERSON> } from "@angular/common";
import { Component, EventEmitter, Input, Output } from "@angular/core";
import { DestroyService } from "@core/services/utils/destroy";
import { ResizeService } from "@core/services/utils/resize";
import { TranslateModule } from "@ngx-translate/core";
import {
  NzButtonComponent,
  NzButtonSize,
  NzButtonType,
} from "ng-zorro-antd/button";
import { NzIconDirective } from "ng-zorro-antd/icon";
import { NzTooltipDirective } from "ng-zorro-antd/tooltip";
import { takeUntil } from "rxjs";

@Component({
  selector: "app-simple-button",
  standalone: true,
  providers: [DestroyService],
  imports: [
    NzButtonComponent,
    NzIconDirective,
    TranslateModule,
    NzTooltipDirective,
    NgStyle,
    NgClass,
  ],
  templateUrl: "./simple-button.component.html",
  styleUrl: "./simple-button.component.less",
})
export class SimpleButtonComponent {
  @Input() title: string;
  @Input() icon: string;
  @Input() type: NzButtonType = "primary";
  @Input() iconOnly: boolean = false;
  @Input() danger: boolean = false;
  @Input() breakpoint: number = 1200;
  @Input() autoMinify: boolean = true;
  @Input() disabled: boolean;
  @Input() tooltipPlacement:
    | "top"
    | "left"
    | "right"
    | "bottom"
    | "topLeft"
    | "topRight"
    | "bottomLeft"
    | "bottomRight"
    | "leftTop"
    | "leftBottom"
    | "rightTop"
    | "rightBottom" = "top";
  @Input() style: { [key: string]: any };
  @Input() iconStyle?: { [key: string]: any };
  @Input() iconTheme: "outline" | "fill" | "twotone" = "outline";
  @Input() size?: NzButtonSize = "default";

  @Output() onButtonClick: EventEmitter<void> = new EventEmitter();

  protected minified: boolean = false;

  constructor(
    private resizeService: ResizeService,
    private destroy$: DestroyService
  ) {
    this.resizeService
      .subscribe()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.checkIfMinified();
      });
  }

  ngOnInit(): void {
    this.checkIfMinified();
  }

  private checkIfMinified() {
    if (this.iconOnly) {
      this.minified = true;
      this.title = null;
    } else {
      if (this.autoMinify) this.minified = window.innerWidth < this.breakpoint;
    }
  }
}
