import { Component, input, output } from "@angular/core";
import { SimpleButtonComponent } from "../simple-button/simple-button.component";

@Component({
  selector: "app-upgrade-plan-button",
  standalone: true,
  imports: [SimpleButtonComponent],
  templateUrl: "./upgrade-plan-button.component.html",
  styleUrl: "./upgrade-plan-button.component.less",
})
export class UpgradePlanButtonComponent {
  onButtonClick = output<void>();
  text = input<string>("UPGRADEPLAN.upgradePlan");

  onUpgradeClick() {
    this.onButtonClick.emit();
  }
}
