import { <PERSON><PERSON>ty<PERSON> } from '@angular/common';
import { Component, EventEmitter, Input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzCheckboxComponent } from 'ng-zorro-antd/checkbox';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';

@Component({
  selector: 'app-input-checkbox',
  standalone: true,
  imports: [
    NgStyle,
    NzCheckboxComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormControlComponent,
    NzFormLabelComponent,
    NzColDirective,
    NzRowDirective,
    TranslateModule
  ],
  templateUrl: './input-checkbox.component.html',
  styleUrl: './input-checkbox.component.less'
})
export class InputCheckboxComponent {
  @Input() parentForm: FormGroup;
  @Input() label: string;
  @Input() controlName: string;
  @Input() name: string;
  @Input() optionList: { label: string; value: any }[] = [];
  @Input() labelPosition: 'left' | 'top' = 'left';
  @Input() style: { [key: string]: string };
  protected onCheckboxChange: EventEmitter<Event> = new EventEmitter<Event>();

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }
}
