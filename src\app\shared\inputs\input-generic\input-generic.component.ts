import { Ng<PERSON>ty<PERSON> } from "@angular/common";
import { Component, Input, TemplateRef } from "@angular/core";
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from "ng-zorro-antd/form";
import { NzColDirective, NzRowDirective } from "ng-zorro-antd/grid";
import { NzInputDirective, NzInputGroupComponent } from "ng-zorro-antd/input";

@Component({
  selector: "app-input-generic",
  standalone: true,
  imports: [
    NgStyle,
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzInputGroupComponent,
    NzInputDirective,
    NzColDirective,
    NzRowDirective,
    TranslateModule,
  ],
  templateUrl: "./input-generic.component.html",
  styleUrl: "./input-generic.component.less",
})
export class InputGenericComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: string = "";
  @Input() pattern: string;
  @Input() type: "text" | "email" = "text";
  @Input() prefixIcon: string;
  @Input() suffixIcon: string;
  @Input() minLength: number = 0;
  @Input() maxLength: number = 999;
  @Input() labelPosition: "left" | "top" = "left";
  @Input() style: { [key: string]: any };
  @Input() showErrorText: boolean = true;
  @Input() suffixAddonText: string | TemplateRef<any>;
  @Input() prefixAddonText: string | TemplateRef<any>;
  @Input() customErrors?: string[];

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style,
      display: this.labelPosition === "left" ? "flex" : "block",
    };
  }
}
