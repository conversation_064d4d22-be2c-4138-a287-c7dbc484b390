<nz-form-item
  [formGroup]="parentForm"
  [ngClass]="{ 'top-label': labelPosition === 'top' }"
>
  @if (!!label) {
    <nz-form-label [nzRequired]="isRequired()" nzNoColon="true">
      {{ label | translate }}
    </nz-form-label>
  }

  <nz-form-control [nzErrorTip]="errorTpl" style="width: 100%">
    <nz-space style="width: 100%">
      <nz-input-number
        *nzSpaceItem
        [formControlName]="controlName"
        [nzPlaceHolder]="placeholder | translate"
        [nzFormatter]="formatterCurrency"
        [nzStep]="step"
        [nzPrecision]="precision"
        [style]="'width: ' + width"
        [nzDisabled]="disabled"
        [nzMin]="minNumber"
        [nzMax]="maxNumber"
        style="width: 100%"
      >
        @if (prefix) {
          <span nzInputAddonBefore>{{ prefix }}</span>
        }
        @if (suffix) {
          <span nzInputAddonAfter>{{ suffix }}</span>
        }
      </nz-input-number>
    </nz-space>

    <ng-template #errorTpl let-control>
      @if (control.dirty && control.touched && control.hasError("required")) {
        {{ "Campo obbligatorio" }}
      }
      @if (control.dirty && control.touched && control.hasError("min")) {
        {{ "Il valore è troppo piccolo" }}
      }
      @if (control.dirty && control.touched && control.hasError("max")) {
        {{ "Il valore è troppo grande" }}
      }
    </ng-template>
  </nz-form-control>
</nz-form-item>
