import { Ng<PERSON><PERSON> } from '@angular/common';
import { Component, Input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzInputNumberComponent } from 'ng-zorro-antd/input-number';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-input-number',
  standalone: true,
  imports: [
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzSpaceComponent,
    NzInputNumberComponent,
    NzSpaceItemDirective,
    FormsModule,
    ReactiveFormsModule,
    NzColDirective,
    NzRowDirective,
    TranslateModule,
    NgClass,
  ],
  templateUrl: './input-number.component.html',
  styleUrl: './input-number.component.less',
})
export class InputNumberComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: string;

  @Input() prefix: string;
  @Input() formatCurrency: boolean = false;
  @Input() formatDecimal: boolean = false;
  @Input() width: string = 'auto';
  @Input() step: number = 0.1;
  @Input() precision: number = 2;
  @Input() disabled: boolean = false;
  @Input() suffix: string;
  @Input() minNumber: number;
  @Input() maxNumber: number;
  @Input() labelPosition: 'left' | 'top' = 'left';
  protected formatterCurrency = (value: number): string => {
    if (!!this.formatCurrency) {
      return (
        value.toLocaleString('en-EN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }) + ' €'
      );
    } else if (!!this.formatDecimal) {
      return value?.toLocaleString('en-EN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    } else {
      return value?.toString();
    }
  };

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }
}
