<nz-form-item [formGroup]="parentForm" [ngStyle]="getCombinedStyles()">
  @if (!!label) {
    <nz-form-label [nzRequired]="isRequired()" nzNoColon="true">
      {{ label | translate }}
    </nz-form-label>
  }

  <nz-form-control nzHasFeedback [nzErrorTip]="errorTpl">
    <nz-input-group [nzPrefixIcon]="prefixIcon" [nzSuffix]="suffixTemplate">
      <input
        nz-input
        [formControlName]="controlName"
        [placeholder]="placeholder"
        [pattern]="pattern"
        [minlength]="minLength"
        [maxlength]="maxLength"
        [type]="passwordVisible ? 'text' : 'password'"
      />
    </nz-input-group>
    <ng-template #errorTpl let-control>
      @if (control.dirty && control.touched && control.hasError("required")) {
        {{ "Campo obbligatorio" }}
      }
      @if (control.dirty && control.touched && control.hasError("pattern")) {
        {{ "Formato non valido" }}
      }
      @if (control.dirty && control.touched && control.hasError("minlength")) {
        {{ "Inserisci almeno " + minLength + " caratteri" }}
      }
      @if (control.dirty && control.touched && control.hasError("maxlength")) {
        {{ "Hai superato il limite di caratteri" }}
      }
      @if (
        control.dirty && control.touched && control.hasError("passwordMismatch")
      ) {
        {{ "Le password non corrispondono" }}
      }
    </ng-template>
  </nz-form-control>
</nz-form-item>

<ng-template #suffixTemplate>
  <span
    nz-icon
    [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
    (click)="passwordVisible = !passwordVisible"
  ></span>
</ng-template>
