import { <PERSON><PERSON><PERSON><PERSON> } from "@angular/common";
import { Component, Input } from "@angular/core";
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import {
  NzForm<PERSON>ontrolComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from "ng-zorro-antd/form";
import { NzColDirective, NzRowDirective } from "ng-zorro-antd/grid";
import { NzIconDirective } from "ng-zorro-antd/icon";
import { NzInputDirective, NzInputGroupComponent } from "ng-zorro-antd/input";

@Component({
  selector: "app-input-password",
  standalone: true,
  imports: [
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzInputGroupComponent,
    NzInputDirective,
    NzIconDirective,
    FormsModule,
    TranslateModule,
    ReactiveFormsModule,
    NzRowDirective,
    NzColDirective,
    NgStyle,
  ],
  templateUrl: "./input-password.component.html",
  styleUrl: "./input-password.component.less",
})
export class InputPasswordComponent {
  @Input() parentForm: FormGroup;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: string = "";
  @Input() pattern: string;
  @Input() prefixIcon: string;
  @Input() minLength: number = 0;
  @Input() maxLength: number = 999;
  @Input() style: { [key: string]: any };
  @Input() labelPosition: "left" | "top" = "left";
  protected passwordVisible = false;

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style,
      display: this.labelPosition === "left" ? "flex" : "block",
    };
  }
}
