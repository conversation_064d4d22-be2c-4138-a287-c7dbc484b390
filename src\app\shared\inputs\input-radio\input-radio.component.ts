import { Component, Input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormLabelComponent
} from 'ng-zorro-antd/form';
import { NzInputDirective } from 'ng-zorro-antd/input';
import { NzRadioComponent } from 'ng-zorro-antd/radio';

@Component({
  selector: 'app-input-radio',
  standalone: true,
  imports: [
    NzRadioComponent,
    NzFormDirective,
    NzInputDirective,
    FormsModule,
    ReactiveFormsModule,
    NzFormControlComponent,
    NzFormLabelComponent
  ],
  templateUrl: './input-radio.component.html',
  styleUrl: './input-radio.component.less'
})
export class InputRadioComponent {
  @Input() parentForm: FormGroup;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: number;
  @Input() width: string = 'auto';
  @Input() disabled: boolean = false;

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }
}
