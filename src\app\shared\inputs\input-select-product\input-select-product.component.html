<nz-form-item [formGroup]="parentForm">
  @if (!!label) {
    <nz-form-label [nzRequired]="isRequired()" nzNoColon="true">
      {{ label }} {{ labelSuffix ? labelSuffix : null }}
    </nz-form-label>
  }
  <nz-form-control>
    <nz-select
      [formControlName]="controlName"
      [nzPlaceHolder]="placeholder"
      [nzMode]="mode"
      [nzSize]="size"
      [nzMaxTagCount]="maxTagCount"
      [nzAllowClear]="allowClear"
      [nzDisabled]="disabled"
      [nzShowSearch]="showSearch"
      [nzServerSearch]="serverSearch"
      [nzOptionHeightPx]="optionHeightPx"
      [compareWith]="compareFn"
      (nzOnSearch)="onSearchClick($event)"
      (ngModelChange)="onProductChangeClick($event)"
    >
      @for (option of optionList; track $index) {
        <nz-option
          [nzLabel]="option[configKey.label]"
          [nzValue]="option"
          [nzCustomContent]="true"
        >
          <img
            [src]="option.images[0].url"
            [alt]="option.variantName"
            [width]="imageWidth"
            [height]="imageHeight"
          />
          <span style="margin-left: 0.5rem">{{ option[configKey.label] }}</span>
        </nz-option>
      }
    </nz-select>
  </nz-form-control>
</nz-form-item>
