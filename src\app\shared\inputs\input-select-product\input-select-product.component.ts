import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { CheckUtils } from '@core/utils/check';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormLabelComponent
} from 'ng-zorro-antd/form';
import { NzInputDirective } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { BehaviorSubject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-input-select-product',
  standalone: true,
  imports: [
    NzFormDirective,
    NzSelectModule,
    NzInputDirective,
    FormsModule,
    ReactiveFormsModule,
    NzFormLabelComponent,
    NzFormControlComponent
  ],
  templateUrl: './input-select-product.component.html',
  styleUrl: './input-select-product.component.less'
})
export class InputSelectProductComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() labelSuffix?: string;
  @Input() placeholder: string;
  @Input() prefixIcon: string;
  @Input() mode: 'multiple' | 'tags' | 'default' = 'default';
  @Input() size: 'large' | 'small' | 'default' = 'default';
  @Input() disabled: boolean = false;
  @Input() optionList: { label: string; value: any }[] | any;
  @Input() configKey: {
    label: string;
    value: string;
  } = {
    label: 'label',
    value: 'value'
  };
  @Input() maxTagCount: number;
  @Input() showSearch: boolean = false;
  @Input() serverSearch: boolean = false;
  @Input() allowClear: boolean = false;
  @Input() optionHeightPx: number = 32;
  @Input() imageWidth: number = 35;
  @Input() imageHeight: number = 35;

  @Output() onSearch = new EventEmitter();
  @Output() onProductChange = new EventEmitter();

  protected searchChange$ = new BehaviorSubject('');

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe(data => {
      if (!CheckUtils.isNullUndefinedOrEmpty(data)) this.onSearch.emit(data);
    });
  }

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  onSearchClick(value: any) {
    this.searchChange$.next(value);
  }

  onProductChangeClick(value: any) {
    this.onProductChange.emit(value);
  }
  compareFn = (o1: any, o2: any): boolean =>
    o1 && o2
      ? o1[this.configKey.value] === o2[this.configKey.value]
      : o1 === o2;
}
