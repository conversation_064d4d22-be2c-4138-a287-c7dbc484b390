import { <PERSON><PERSON>ty<PERSON> } from "@angular/common";
import { Component, EventEmitter, Input, Output } from "@angular/core";
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { CheckUtils } from "@core/utils/check";
import { TranslateModule } from "@ngx-translate/core";
import {
  NzFormControlComponent,
  NzFormLabelComponent,
} from "ng-zorro-antd/form";
import { NzSelectModule } from "ng-zorro-antd/select";
import { BehaviorSubject, debounceTime } from "rxjs";

@Component({
  selector: "app-input-select",
  standalone: true,
  imports: [
    NzSelectModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormLabelComponent,
    NzFormControlComponent,
    TranslateModule,
    NgStyle,
  ],
  templateUrl: "./input-select.component.html",
  styleUrl: "./input-select.component.less",
})
export class InputSelectComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: string;
  @Input() prefixIcon: string;
  @Input() mode: "multiple" | "tags" | "default" = "default";
  @Input() hideSelectedOptions: boolean = false;
  @Input() size: "large" | "small" | "default" = "default";
  @Input() disabled: boolean = false;
  @Input() optionList: { label: string; value: any }[] | any;
  @Input() configKey: {
    label: string;
    value: string;
  } = {
    label: "label",
    value: "value",
  };
  @Input() maxTagCount: number = 5;
  @Input() showSearch: boolean = false;
  @Input() serverSearch: boolean = false;
  @Input() allowClear: boolean = false;
  @Input() labelPosition: "left" | "top" = "left";
  @Output() onSearch = new EventEmitter();

  protected searchChange$ = new BehaviorSubject("");

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe((data) => {
      if (!CheckUtils.isNullUndefinedOrEmpty(data)) this.onSearch.emit(data);
    });
  }

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  onSearchClick(value: any) {
    this.searchChange$.next(value);
  }

  compareFn = (o1: any, o2: any): boolean => {
    return o1 && o2
      ? o1[this.configKey.value] === o2[this.configKey.value]
      : o1 === o2;
  };
}
