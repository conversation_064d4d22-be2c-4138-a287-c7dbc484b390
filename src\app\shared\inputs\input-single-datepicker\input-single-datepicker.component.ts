import { Ng<PERSON>ty<PERSON> } from "@angular/common";
import { Component, Input } from "@angular/core";
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { NzDatePickerComponent } from "ng-zorro-antd/date-picker";
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from "ng-zorro-antd/form";

@Component({
  selector: "app-input-single-datepicker",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzDatePickerComponent,
    NgStyle,
    TranslateModule,
  ],

  templateUrl: "./input-single-datepicker.component.html",
  styleUrl: "./input-single-datepicker.component.less",
})
export class InputSingleDatepickerComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() placeholder: string;
  @Input() style: { [key: string]: any };
  @Input() labelPosition: "left" | "top" = "left";
  @Input() showNow: boolean = false;
  @Input() disabledDates: (current: Date) => boolean;
  @Input() nzFormat: string = "yyyy-MM-dd";
  @Input() allowClear: boolean = false;

  isStartRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  isEndRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  handleEndOpenChange(open: boolean): void {
    // log('handleEndOpenChange', open);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style,
      display: this.labelPosition === "left" ? "flex" : "block",
    };
  }
}
