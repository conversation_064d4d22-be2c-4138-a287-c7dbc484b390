import { Component, Input, ViewChild } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { NzDatePickerComponent } from 'ng-zorro-antd/date-picker';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent
} from 'ng-zorro-antd/form';

@Component({
  selector: 'app-input-start-end-date',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzDatePickerComponent
  ],
  templateUrl: './input-start-end-date.component.html',
  styleUrl: './input-start-end-date.component.less'
})
export class InputSartEndDateComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlNameStart: string;
  @Input() controlNameEnd: string;
  @Input() labelStart: string;
  @Input() labelEnd: string;
  @Input() placeholderStart: string;
  @Input() placeholderEnd: string;

  protected startValue: Date | null = null;
  protected endValue: Date | null = null;
  @ViewChild('endDatePicker') endDatePicker!: NzDatePickerComponent;

  disabledStartDate = (startValue: Date): boolean => {
    if (!startValue || !this.endValue) {
      return false;
    }
    return startValue.getTime() > this.endValue.getTime();
  };

  disabledEndDate = (endValue: Date): boolean => {
    if (!endValue || !this.startValue) {
      return false;
    }
    return endValue <= this.startValue;
  };

  isStartRequired() {
    return this.parentForm
      .get(this.controlNameStart)
      .hasValidator(Validators.required);
  }

  isEndRequired() {
    return this.parentForm
      .get(this.controlNameEnd)
      .hasValidator(Validators.required);
  }

  handleStartOpenChange(open: boolean): void {
    if (!open) {
      this.endDatePicker.open();
    }
    // log('handleStartOpenChange', open);
  }

  handleEndOpenChange(open: boolean): void {
    // log('handleEndOpenChange', open);
  }
}
