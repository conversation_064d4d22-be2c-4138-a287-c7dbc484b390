import { Component, Input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormItemComponent,
  NzFormLabelComponent
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzAutosizeDirective, NzInputDirective } from 'ng-zorro-antd/input';

@Component({
  selector: 'app-input-textarea',
  standalone: true,
  imports: [
    NzInputDirective,
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzAutosizeDirective,
    NzFormItemComponent,
    NzColDirective,
    NzRowDirective
  ],
  templateUrl: './input-textarea.component.html',
  styleUrl: './input-textarea.component.less'
})
export class InputTextareaComponent {
  @Input() parentForm: FormGroup | any;
  @Input() label: string;
  @Input() controlName: string;
  @Input() placeholder: string;
  @Input() minLength: number = 0;
  @Input() maxLength: number = 999;
  @Input() size: { minRows: number; maxRows: number };

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }
}
