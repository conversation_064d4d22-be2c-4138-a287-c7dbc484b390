import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzInputDirective } from 'ng-zorro-antd/input';
import { ITableColumn } from '../types/table.column';

@Component({
  selector: 'th-search-filter',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  styles: `
  .search-input {
    width: 188px;
    margin-bottom: 8px;
    display: block;
  }

  .search-buttons {
    display:flex;
    align-items:center;
    justify-content:space-between;
  }
  `,
  imports: [TranslateModule, FormsModule, NzInputDirective, NzButtonComponent],
  template: `
    <input
      type="text"
      class="search-input"
      nz-input
      placeholder="{{ 'TABLE.search' | translate }} {{
        column.title | translate
      }}"
      [ngModel]="searchValue"
      (ngModelChange)="onSetCustomSearchItem.emit($event)"
    />
    <div class="search-buttons">
      <button
        nz-button
        nzSize="small"
        style="margin-right: 8px;"
        (click)="onResetSearch.emit()"
      >
        {{ 'TABLE.reset' | translate }}
      </button>
      <button
        nz-button
        nzSize="small"
        nzType="primary"
        (click)="onCustomSearch.emit()"
      >
        {{ 'TABLE.search' | translate }}
      </button>
    </div>
  `
})
export class ThSearchFilterComponent {
  @Input() column: ITableColumn;
  @Input() searchValue: any;

  @Output() onSetCustomSearchItem: EventEmitter<any> = new EventEmitter();
  @Output() onCustomSearch: EventEmitter<void> = new EventEmitter();
  @Output() onResetSearch: EventEmitter<void> = new EventEmitter();
}
