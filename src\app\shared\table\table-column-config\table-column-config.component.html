<div class="table-columns">
  <button
    nz-button
    nzSize="small"
    nz-dropdown
    [nzDropdownMenu]="menu_columns"
    [nzTrigger]="'click'"
    [(nzVisible)]="columnPopoverVisible"
    [nzPlacement]="'bottomRight'"
  >
    {{ "TABLE.columns" | translate
    }}<i
      nz-icon
      [nzType]="columnPopoverVisible ? 'up' : 'down'"
      nzTheme="outline"
    ></i>
  </button>

  <nz-dropdown-menu #menu_columns="nzDropdownMenu">
    <nz-list
      cdkDropList
      (cdkDropListDropped)="dropItem($event)"
      nzItemLayout="horizontal"
      [nzSize]="'small'"
      [nzBordered]="false"
      class="dropdown-column-list shadowing"
    >
      <nz-list-header class="column-dropdown-header">
        <div class="column-dropdown-title">
          {{ "TABLE.tableColumnSettingsDropdownTitle" | translate }}
        </div>
        <div class="column-dropdown-subtitle">
          {{ "TABLE.tableColumnSettingsDropdownSubTitle" | translate }}
        </div>
        <div nz-row class="column-dropdown-actions">
          <div nz-col nzSpan="12">
            <a (click)="resetColumns()">
              <i nz-icon nzType="reload" nzTheme="outline"></i>
              {{ "TABLE.reset" | translate }}
            </a>
          </div>
          <div nz-col nzSpan="12" style="text-align: right">
            <a
              *ngIf="listOfColumns | areAllColumnsHide"
              (click)="setAllColumnsVisibility(true)"
            >
              <i nz-icon nzType="eye" nzTheme="outline"></i>
              {{ "TABLE.showAll" | translate }}
            </a>
            <a
              *ngIf="!(listOfColumns | areAllColumnsHide)"
              (click)="setAllColumnsVisibility(false)"
            >
              <i nz-icon nzType="eye-invisible" nzTheme="outline"></i>
              {{ "TABLE.hideAll" | translate }}
            </a>
          </div>
        </div>
      </nz-list-header>
      <div class="column-list">
        <ng-container
          *ngFor="let item of listOfColumns; trackBy: trackByColumn"
        >
          <nz-list-item
            *ngIf="!item.lockOnLeft && !item.lockOnRight"
            cdkDrag
            cdkDragLockAxis="y"
          >
            <ng-container *ngTemplateOutlet="tplColListItem"></ng-container>
          </nz-list-item>
          <nz-list-item *ngIf="item.lockOnLeft || item.lockOnRight">
            <ng-container *ngTemplateOutlet="tplColListItem"></ng-container>
          </nz-list-item>

          <ng-template #tplColListItem>
            <div nz-row style="width: 100%; font-size: 13px">
              <div nz-col [nzSpan]="3">
                <a
                  *ngIf="!item.lockOnLeft && !item.lockOnRight"
                  class="drag-and-drop"
                >
                  <i nz-icon nzType="biuti-ui:drag-drop" nzTheme="outline"></i>
                </a>
                <i
                  *ngIf="item.lockOnLeft || item.lockOnRight"
                  nz-icon
                  nzType="lock"
                  nzTheme="outline"
                ></i>
              </div>
              <div nz-col [nzSpan]="17">
                <div nz-row style="font-weight: 600">
                  {{ item.title | translate }}
                </div>
              </div>
              <div nz-col [nzSpan]="4" style="text-align: right">
                <nz-switch
                  nzSize="small"
                  [(ngModel)]="item.visible"
                  [nzDisabled]="item.lockOnLeft || item.lockOnRight"
                  (ngModelChange)="setDisplayedColumnNames()"
                ></nz-switch>
              </div>
            </div>
          </ng-template>
        </ng-container>
      </div>
    </nz-list>
  </nz-dropdown-menu>
</div>
