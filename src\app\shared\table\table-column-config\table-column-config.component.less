//Column Dropdown
.ant-dropdown-trigger > .anticon.anticon-up,
.ant-dropdown-link > .anticon.anticon-up,
.ant-dropdown-button > .anticon.anticon-up {
  font-size: 10px;
  vertical-align: baseline;
}

// TABLE COLUMNS DRAG & DROP
.dropdown-column-list {
  width: 260px;
  overflow: hidden;
}

.column-dropdown-header {
  padding: 0;
}

.column-dropdown-title {
  padding: 8px 16px 0;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 13px;
}

.column-dropdown-subtitle {
  padding: 0 16px 8px;
  font-size: 12px;
}

.column-dropdown-actions {
  padding: 0 16px 4px;
  font-size: 13px;
}

.column-list {
  max-height: 300px;
  overflow-y: auto;
}

.cdk-drag-preview {
  filter: drop-shadow(0 0 0.4rem #2e2e2e);
  padding: 8px 16px;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.shadowing {
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.drag-and-drop {
  cursor: -webkit-grab;
  cursor: grab;
}

.drag-and-drop:active {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}
