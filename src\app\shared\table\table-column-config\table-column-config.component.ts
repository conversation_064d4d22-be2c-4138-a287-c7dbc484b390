import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray
} from '@angular/cdk/drag-drop';
import { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input, OnInit,
  Output
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzDropDownDirective,
  NzDropdownMenuComponent
} from 'ng-zorro-antd/dropdown';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import {
  NzListComponent,
  NzListHeaderComponent,
  NzListItemComponent
} from 'ng-zorro-antd/list';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { AreAllColumnsHidePipe } from '../pipes/are-all-columns-hide.pipe';
import { TableConfigService } from '../table-config.service';
import { TableQueryService } from '../table-query.service';
import { ITableColumn } from '../types/table.column';

@Component({
  selector: 'app-table-column-config',
  standalone: true,
  imports: [
    NzSwitchComponent,
    NzDropDownDirective,
    NzButtonComponent,
    DragDropModule,
    NgIf,
    NgFor,
    NgTemplateOutlet,
    TranslateModule,
    NzIconDirective,
    AreAllColumnsHidePipe,
    FormsModule,
    NzListComponent,
    NzDropdownMenuComponent,
    NzListHeaderComponent,
    NzListItemComponent,
    NzColDirective,
    NzRowDirective
  ],
  templateUrl: './table-column-config.component.html',
  styleUrl: './table-column-config.component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableColumnConfigComponent implements OnInit {
  @Input() listOfColumns: ITableColumn[];
  @Input() tableName: string;
  @Output() listOfColumnsChange: EventEmitter<ITableColumn[]> =
    new EventEmitter();
  @Output() resetColumnEvent: EventEmitter<any> = new EventEmitter<any>();

  public columnPopoverVisible: boolean = false;
  public displayedColumnObjects: ITableColumn[];
  public columnsToDisplay: string[];

  private leftLockOffset: number = 0;
  private rightLockOffset: number = 0;

  constructor(
    private tableQueryService: TableQueryService,
    private tableConfigService: TableConfigService
  ) {
  }

  ngOnInit(): void {
    this.initialColumnsSettings();
  }

  trackByColumn(_: number, item: ITableColumn): any {
    return item.id;
  }

  private initialColumnsSettings() {
    this.leftLockOffset = this.listOfColumns.filter(
      col => col.lockOnLeft == true
    ).length;
    this.rightLockOffset = this.listOfColumns.filter(
      col => col.lockOnRight == true
    ).length;
  }

  resetColumns(): void {
    this.setAllColumnsVisibility(true);
    this.initialColumnsSettings();
    this.setDisplayedColumnNames();
    this.tableConfigService.updateColumn$.next(null);
    this.resetColumnEvent.emit();
  }

  /** Set All Columns Visibility
   * @param visible
   */
  public setAllColumnsVisibility(visible: boolean): void {
    this.listOfColumns.forEach(col => {
      if (col.lockOnLeft || col.lockOnRight) {
        col.visible = true;
      } else col.visible = visible;
    });
    this.setDisplayedColumnNames();
  }

  /** Set columns Dispalyed Name */
  public setDisplayedColumnNames(): void {
    const displayedColumns: { columnId: string; visibility: boolean }[] =
      this.listOfColumns.map((column: ITableColumn) => ({
        columnId: column.id,
        visibility: column.visible
      }));
  }

  /** Set Columns to Display */
  private loadColumnsToDisplay(): void {
    this.setDisplayedColumnNames();
  }

  /** Drag&Drop: Columns setting */
  dropItem(event: CdkDragDrop<string[]>): void {
    moveItemInArray(
      this.listOfColumns,
      event.previousIndex + this.leftLockOffset,
      event.currentIndex + this.leftLockOffset
    );
    this.tableConfigService.updateColumn$.next(null);
  }

  /** Sort list of columns by persistency obj*/
  private loadColumnsIndex() {
    let sortedListOfColumn: ITableColumn[] = [];
    this.listOfColumns = [...sortedListOfColumn];
  }
}
