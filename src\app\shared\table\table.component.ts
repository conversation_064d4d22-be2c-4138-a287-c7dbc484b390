import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  NgTemplateOutlet,
} from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { drawerMaskDirective } from '@core/directives/drawer-mask.directive';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { DestroyService } from '@core/services/utils/destroy';
import { ResizeService } from '@core/services/utils/resize';
import { CheckUtils } from '@core/utils/check';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzDrawerComponent,
  NzDrawerContentDirective,
} from 'ng-zorro-antd/drawer';
import {
  NzDropDownDirective,
  NzDropdownMenuComponent,
} from 'ng-zorro-antd/dropdown';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import {
  NzMenuDirective,
  NzMenuItemComponent,
  NzSubMenuComponent,
} from 'ng-zorro-antd/menu';
import { NzSliderComponent } from 'ng-zorro-antd/slider';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';
import {
  NzCellAlignDirective,
  NzCellFixedDirective,
  NzFilterTriggerComponent,
  NzTableComponent,
  NzTableQueryParams,
  NzTableVirtualScrollDirective,
  NzTdAddOnComponent,
  NzThAddOnComponent,
  NzThMeasureDirective,
  NzThSelectionComponent,
  NzTheadComponent,
  NzTrDirective,
} from 'ng-zorro-antd/table';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { Subscription, interval, takeUntil } from 'rxjs';
import { ActiveFiltersComponent } from './active-filters/active-filters.component';
import { ThDateFilterComponent } from './addon/th-date-filter.component';
import { ThMultipleFilterComponent } from './addon/th-multiple-filter.component';
import { ThSearchFilterComponent } from './addon/th-search-filter.component';
import { GetCellValuePipe } from './pipes/get-cell-value.pipe';
import { HasColumnFilterPipe } from './pipes/has-column-filter.pipe';
import { HasMultipleRowActionsSecondaryActionsPipe } from './pipes/has-multiple-row-actions-secondary-actions.pipe';
import { IsSingleRowActionVisiblePipe } from './pipes/is-single-row-action-visible.pipe';
import { TableFilterIconPipe } from './pipes/table-filter-icon.pipe';
import { TableColumnConfigComponent } from './table-column-config/table-column-config.component';
import { TableConfigService } from './table-config.service';
import { TableQueryService } from './table-query.service';
import { TableModule } from './table.module';
import { ITableRowAction } from './types/table.action';
import { ITableColumn, requestFilterOperatorType } from './types/table.column';
import { ITableFilterItem, tableFilterType } from './types/table.filter';
import { ITableQuery } from './types/table.query';
import { IRefreshData } from './types/table.refresh-data';
import { ITableSetting } from './types/table.settings';

@Component({
  selector: 'app-table',
  standalone: true,
  imports: [
    FormsModule,
    NgStyle,
    TranslateModule,
    NgIf,
    NgFor,
    NgClass,
    NgTemplateOutlet,
    HasColumnFilterPipe,
    TableFilterIconPipe,
    IsSingleRowActionVisiblePipe,
    HasMultipleRowActionsSecondaryActionsPipe,
    GetCellValuePipe,
    NullValuePipe,
    ThSearchFilterComponent,
    ThDateFilterComponent,
    ThMultipleFilterComponent,
    ActiveFiltersComponent,
    TableColumnConfigComponent,
    NzSpaceComponent,
    NzTagComponent,
    NzIconDirective,
    NzDropdownMenuComponent,
    NzDropDownDirective,
    NzSubMenuComponent,
    NzMenuItemComponent,
    NzSliderComponent,
    NzTooltipDirective,
    NzTableComponent,
    NzTheadComponent,
    NzThMeasureDirective,
    NzThSelectionComponent,
    NzThAddOnComponent,
    NzFilterTriggerComponent,
    NzTableVirtualScrollDirective,
    NzTdAddOnComponent,
    NzTrDirective,
    NzDrawerComponent,
    drawerMaskDirective,
    NzCellFixedDirective,
    NzCellAlignDirective,
    NzMenuDirective,
    NzRowDirective,
    NzColDirective,
    NzSpaceItemDirective,
    NzButtonComponent,
    TableModule,
    NzDrawerContentDirective,
  ],
  templateUrl: './table.component.html',
  styleUrl: './table.component.less',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableComponent implements OnInit, AfterViewInit, OnChanges {
  @ViewChild('virtualTable', { static: false })
  nzTableComponent?: NzTableComponent<any>;
  /** Table settings */
  @Input() tableLayoutSettings: ITableSetting;
  /** Data */
  @Input() data: any;
  /** Refresh slider settings */
  @Input() refresh: IRefreshData;
  /** Table Refresh button  */
  @Input() manualRefreshButton: boolean = false;
  /** Table Name for persistent Filter */
  @Input() tableId: string;
  /** Input max number of active filters tag before "more tag" */
  @Input() maxActiveFilterTagCount: number = 6;
  /** Input flag to show/unshow share and/or save button */
  @Input() saveActiveFilters: boolean;
  /** View loading status */
  @Input() loading: boolean = false;
  /** Pagination Options */
  @Input() paginationOptions: number[] = [10, 20, 50, 100];
  /**The size of the row in the list, same as cdk itemSize.
   * By default the size of the table is "small" with a width for each row of 40px. */
  @Input() rowSize: number = 40;
  /** Whether table can be scrolled in x direction. x  can be a string that indicates the width of table body (by default 1100px)*/
  @Input() tableXAxis: string = '1100px';
  /** Whether table can be scrolled in y direction. y can be a string that indicates the height of table body (if dynamicTableHeight is true value is auto-calculated)*/
  @Input() tableYAxis: string;

  //Drawer multiselection config
  @Input() drawerConfig: {
    rightTemplate?: TemplateRef<any>;
    centerTemplate?: TemplateRef<any>;
  };
  /** Enable select all (by default true) */
  @Input() allRowsSelection: boolean = true;
  /** Event triggered when every single row is selected */
  @Output() onRowSelected: EventEmitter<Map<number, any>> = new EventEmitter();
  /** Event triggered when every params change (pageSize, pageIndex,filter change) */
  @Output() onCurrentPageDataChange: EventEmitter<any> = new EventEmitter();
  /** Event triggered when on refresh is clicked. (It's possible to define callback function) */
  @Output() onRefreshButtonClick: EventEmitter<any> = new EventEmitter();
  /** Event triggered on single row context menu action click. (It's possible to define callback function) */
  @Output() onSingleRowAction: EventEmitter<any> = new EventEmitter();
  /** Event triggered on multiple row context menu action click. (It's possible to define callback function) */
  @Output() onMultipleRowAction: EventEmitter<any> = new EventEmitter();
  /** Event triggered on refresh timer elapsed. */
  @Output() onRefreshTimerTick: EventEmitter<void> = new EventEmitter();
  /** Event triggered on refresh timer elapsed. */
  @Output() onQueryChange: EventEmitter<ITableQuery> = new EventEmitter();

  protected checkUtils = CheckUtils;

  protected checked: boolean = false;
  protected indeterminate: boolean = false;
  protected mapOfCheckedItem = new Map<number, any>();
  protected customSearchItems: Array<ITableFilterItem> = [];
  private listOfColumnsBackup: ITableColumn[];
  private updateSubscription: Subscription = new Subscription();

  constructor(
    private tableConfigService: TableConfigService,
    private tableQueryService: TableQueryService,
    private destroy$: DestroyService,
    private resizeService: ResizeService,
  ) {
    this.resizeService
      .subscribe()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.detectTableHeight();
      });

    this.tableQueryService.query$
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        this.onQueryChange.emit(data);
      });

    this.tableConfigService.updateColumn$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        let sortedListOfColumn: ITableColumn[] = [];
        let newCustomSearchItem: ITableFilterItem[] = [];
        this.tableLayoutSettings.listOfColumns.forEach((col) => {
          sortedListOfColumn.push(col);
        });
        sortedListOfColumn.forEach((col) => {
          this.customSearchItems.forEach((item) => {
            item.key == col.id ? newCustomSearchItem.push(item) : null;
          });
        });
        this.customSearchItems = [...newCustomSearchItem];
        this.tableLayoutSettings.listOfColumns = [...sortedListOfColumn];
        this.tableQueryService.updateCustomFilters(
          this.customSearchItems,
          true,
        );
      });
  }

  //-----------CONFIG TABLE--------------
  ngOnChanges(changes: SimpleChanges): void {
    const { data } = changes;
    if (data && !CheckUtils.isNullUndefinedOrEmpty(data.currentValue)) {
      this.buildDataPositions();
      // this.detectTableHeight();
    }
  }

  ngOnInit(): void {
    this.initFilters();
    this.listOfColumnsBackup = [...this.tableLayoutSettings.listOfColumns];
    if (this.refresh) this.setRefreshInterval();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 300);
  }

  /** Filtering & Sorting */
  /** Table Native Params change Event */
  public onQueryParamsChange(params: NzTableQueryParams): void {
    this.tableQueryService.updateQueryParams(params);
    this.detectTableHeight();
  }

  /** On Displayed data change */
  onPageDataChange(listOfCurrentPageData: readonly any[]): void {
    this.onCurrentPageDataChange.emit(listOfCurrentPageData);
  }

  /** Set Refresh Interval */
  setRefreshInterval() {
    if (this.updateSubscription) this.updateSubscription.unsubscribe();

    if (this.refresh.interval > 0)
      this.updateSubscription = interval(this.refresh.interval * 1000)
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          this.refreshIntervalElapsed();
        });
  }

  /** Event emitter when refresh iterval is elapsed */
  private refreshIntervalElapsed(): void {
    this.saveActiveFilters;
    this.onRefreshTimerTick.emit();
  }

  trackByColumn(_: number, item: ITableColumn): any {
    return item.id;
  }

  trackByIndex(_: number, data: any): number {
    return data.index;
  }

  /** Build data position (for row selection) */
  private buildDataPositions(): void {
    const index =
      this.tableLayoutSettings.pagination.pageIndex *
      this.tableLayoutSettings.pagination.pageSize;
    let itemsPerPage = index - this.tableLayoutSettings.pagination.pageSize;

    if (this.data && this.tableLayoutSettings.showRowSelection) {
      this.data.forEach((elem: any) => {
        elem.index = itemsPerPage++;
      });
    } else {
      this.deselectAll();
    }
  }

  /** Detect table height */
  private detectTableHeight(): void {
    let viewportHeight =
      window.innerHeight - this.tableLayoutSettings.dynamicTableHeightOffset;
    this.tableYAxis = viewportHeight + 'px';
  }

  //--------FILTER-----------
  /** Set Custom Search Item when fitler change */
  public onFilterChange(event: any, id: string) {
    this.setCustomSearchItem(event, id);
    this.onCustomSearch();
    this.deselectAll();
  }

  initFilters() {
    this.initializeCustomSearchItems();

    /** Subscribe to tableColumn changes and update table column filters */
    this.tableConfigService.tableColumFilter$
      .pipe(takeUntil(this.destroy$))
      .subscribe((colId) => {
        this.updateTableFilters(colId);
      });

    /** Subscribe to closeAllColumnFilters observable and update table column filters when clase all */
    this.tableConfigService.closeAllColumnFilters$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.initTable();
      });
  }

  /**
   * Restore table column to initial value when close all filter is clicked
   */
  private initTable() {
    this.resetAllFilters();
    this.tableQueryService.updateCustomFilters(this.customSearchItems);
    this.onAllChecked(false);
  }

  private resetAllFilters() {
    this.tableLayoutSettings.listOfColumns.forEach((col) => {
      if (col.listOfFilter && col.listOfFilter.length > 0) {
        col.listOfFilter.forEach((filter) => {
          filter.byDefault = false;
        });
        col.listOfFilter = [...col.listOfFilter];
        col.filterValue = null;
        this.customSearchItems.find((column) => column.key === col.id).value =
          null;
      } else {
        col.filterValue = null;
        this.customSearchItems.find((column) => column.key === col.id).value =
          null;
      }
    });
  }

  /** Initialize custom search value */
  private initializeCustomSearchValue(column: ITableColumn): any {
    return column.filterValue ? column.filterValue : null;
  }

  /** Initialize custom search item */
  private initializeCustomSearchItems(): void {
    if (this.customSearchItems.length == 0) {
      this.customSearchItems = [];
      this.tableLayoutSettings.listOfColumns.forEach((col) => {
        this.customSearchItems.push({
          key: col.id,
          value: this.initializeCustomSearchValue(col),
          visible: false,
          filterTypeof: this.getFilterTypeof(col),
          filterOperator: this.getFilterOperator(col),
          filterType: this.getFilterType(col),
        });
      });
    }
    this.tableQueryService.updateCustomFilters(this.customSearchItems, true);
  }

  private getFilterType(col: ITableColumn): tableFilterType {
    switch (true) {
      case col.hasSearchFilter:
        return tableFilterType.search;
      case col.hasDateFilter:
        return tableFilterType.date;
      case col.hasSingleDateFilter:
        return tableFilterType.singleDate;
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        col.filterMultiple:
        return tableFilterType.multiple;
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        !col.filterMultiple:
        return tableFilterType.radio;
      default:
        return null;
    }
  }

  private getFilterOperator(
    col: ITableColumn,
  ): requestFilterOperatorType | requestFilterOperatorType[] {
    switch (true) {
      case !!col.filterOperator:
        return col.filterOperator;
      case col.hasSearchFilter:
        return requestFilterOperatorType.partString;
      case col.hasDateFilter:
        return [requestFilterOperatorType.ge, requestFilterOperatorType.le];
      case col.hasSingleDateFilter:
        return requestFilterOperatorType.ge;
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        col.filterMultiple:
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        !col.filterMultiple:
        return requestFilterOperatorType.eq;
      default:
        return null;
    }
  }

  private getFilterTypeof(col: ITableColumn): string {
    switch (true) {
      case !!col.filterTypeof:
        return col.filterTypeof;
      case col.hasDateFilter:
      case col.hasSingleDateFilter:
        return 'timestamp';
      case col.hasSearchFilter:
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        col.filterMultiple:
      case col.listOfFilter &&
        col.listOfFilter.length > 0 &&
        !col.filterMultiple:
        return 'string';
      default:
        return null;
    }
  }

  /**Set custom search item */
  public setCustomSearchItem(event: any, id: string) {
    this.customSearchItems.filter((item) => item.key === id)[0].value = event;
  }

  /** on Custom Search */
  public onCustomSearch(): void {
    this.customSearchItems.forEach((v) => (v.visible = false));
    this.tableQueryService.updateCustomFilters(this.customSearchItems);
  }

  /** Reset Custom Search Item */
  public customSearchReset(column: ITableColumn): void {
    column.filterValue = null;
    this.customSearchItems.find((col) => col.key === column.id).value = null;
    this.onCustomSearch();
  }

  /** Set Date Search Item */
  public setDateSearchItem(event: Date | Date[], id: string) {
    this.setCustomSearchItem(event, id);
    this.onCustomSearch();
  }

  /** Update table Filters */
  private updateTableFilters(colId: string) {
    this.tableLayoutSettings.listOfColumns.forEach((col) => {
      if (col.id === colId)
        switch (true) {
          case col.listOfFilter && col.listOfFilter.length > 0:
            this.customSearchReset(col);
            col.listOfFilter.forEach((filter) => {
              filter.byDefault = false;
            });
            col.listOfFilter = [...col.listOfFilter];
            break;
          case col.hasSearchFilter:
            this.customSearchReset(col);
            break;
          case col.hasDateFilter:
          case col.hasSingleDateFilter:
            this.setDateSearchItem(null, col.id);
            break;
        }
    });
    this.onAllChecked(false);
  }

  /**
   * Change refresh interval
   * @param event (number in sec)
   */
  public changeRefreshInterval(event: any): void {
    this.refresh.interval = event;
    this.setRefreshInterval();
  }

  //---------ROW SELECTION---------------

  /**Select all rows */
  selectAll(): void {
    this.onAllChecked(true);
  }

  /**Deselect all rows */
  deselectAll(): void {
    this.mapOfCheckedItem = new Map();
    this.checked = false;
    this.onAllChecked(false);
  }

  /** On all row checked */
  onAllChecked(value: boolean): void {
    this.data.forEach((item) => {
      if (
        !this.disableSelectionFn(
          this.tableLayoutSettings.rowDisableSelectionFn,
          item,
        )
      )
        this.updateCheckedMap(item.index, value, item);
    });
    this.refreshCheckedStatus();
    this.onRowSelected.emit(this.mapOfCheckedItem);
  }

  disableSelectionFn(rowDisableFn: Function, row: any): boolean {
    let result: boolean = false;
    if (!CheckUtils.isNullUndefinedOrEmpty(rowDisableFn)) {
      result = rowDisableFn(row);
    }
    return result;
  }

  /** Refresh checked status */
  refreshCheckedStatus(): void {
    if (CheckUtils.isNullUndefinedOrEmpty(this.data)) this.checked = false;
    else
      this.checked = this.data.every(({ index }) =>
        this.mapOfCheckedItem.has(index),
      );
    this.indeterminate =
      this.data.some(({ index }) => this.mapOfCheckedItem.has(index)) &&
      !this.checked;
  }

  /**Update Map object with row checked */
  updateCheckedMap(pos: number, checked: boolean, item: any): void {
    checked
      ? this.mapOfCheckedItem.set(pos, item)
      : this.mapOfCheckedItem.delete(pos);
  }

  /** On single row checked */
  onRowChecked(pos: number, checked: boolean, item: any): void {
    this.updateCheckedMap(pos, checked, item);
    this.refreshCheckedStatus();
    this.onRowSelected.emit(this.mapOfCheckedItem);
  }

  /** single row callback function when click */
  onSingleRowActionClick(rowAction: ITableRowAction, row: any): void {
    if (!rowAction.disabled) rowAction.callbackFn(row);
  }

  /** On Drawer close */
  onCheckDrawerClose(): void {
    this.mapOfCheckedItem.clear();
  }

  /** multiple row callback function when click */
  onMultipleRowActionClick(rowAction: ITableRowAction): void {
    rowAction.callbackFn(this.mapOfCheckedItem);
  }

  refreshData() {
    !this.onRefreshButtonClick.length
      ? this.tableQueryService.sendQueryObject()
      : this.onRefreshButtonClick.emit();
  }

  resetColumns() {
    this.tableLayoutSettings.listOfColumns = [...this.listOfColumnsBackup];
    this.setDisplayedColumnNames();
  }

  /** Set columns Dispalyed Name */
  public setDisplayedColumnNames(): void {
    // const displayedColumnsBackup: { columnId: string; visibility: boolean }[] =
    //   this.tableLayoutSettings.listOfColumns.map((column: ITableColumn) => ({
    //     columnId: column.id,
    //     visibility: column.visible
    //   }));
  }
}
