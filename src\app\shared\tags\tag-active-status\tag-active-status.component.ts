import { Component, Input, OnChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTagComponent } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-tag-active-status',
  standalone: true,
  imports: [NzTagComponent, NzIconDirective, TranslateModule],
  templateUrl: './tag-active-status.component.html',
  styleUrl: './tag-active-status.component.less'
})
export class TagActiveStatusComponent implements OnChanges {
  @Input() value: boolean;
  protected text: string;
  protected icon: string;
  protected color: string;

  ngOnChanges(): void {
    this.setTag();
  }

  setTag() {
    switch (true) {
      case !!this.value:
        this.text = 'TAGS.active';
        this.icon = 'check-circle';
        this.color = 'success';
        break;
      default:
        this.text = 'TAGS.notActive';
        this.icon = 'close-circle';
        this.color = 'error';
        break;
    }
  }
}
