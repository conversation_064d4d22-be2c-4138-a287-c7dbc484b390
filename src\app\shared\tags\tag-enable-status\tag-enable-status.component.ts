import { Component, Input, OnChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTagComponent } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-tag-enable-status',
  standalone: true,
  imports: [NzTagComponent, NzIconDirective, TranslateModule],
  templateUrl: './tag-enable-status.component.html',
  styleUrl: './tag-enable-status.component.less'
})
export class TagEnableStatusComponent implements OnChanges {
  @Input() value: boolean;
  protected text: string;
  protected icon: string;
  protected color: string;

  constructor() {
  }

  ngOnChanges(): void {
    this.setTag();
  }

  setTag() {
    switch (true) {
      case !!this.value:
        this.text = 'TAGS.enabled';
        this.icon = 'unlock';
        this.color = 'success';
        break;
      default:
        this.text = 'TAGS.disabled';
        this.icon = 'lock';
        this.color = 'error';
        break;
    }
  }
}
