import { Component, input, model, output } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { NzCheckboxComponent } from "ng-zorro-antd/checkbox";

@Component({
  selector: "app-toggle-checkbox",
  standalone: true,
  imports: [NzCheckboxComponent, FormsModule, TranslateModule],
  templateUrl: "./toggle-checkbox.component.html",
  styleUrl: "./toggle-checkbox.component.less",
})
export class ToggleCheckboxComponent {
  checked = model<boolean>();
  label = input<string>();
  disabled = input<boolean>();
  onCheckboxChange = output<void>();
}
