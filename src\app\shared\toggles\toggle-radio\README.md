# Toggle Radio Component

A reusable radio button group component that follows the established patterns in the codebase.

## Features

- Modern Angular signals API (`input()`, `model()`, `output()`)
- Support for custom option lists with configurable keys
- Multiple button styles (solid/outline)
- Different sizes (large/small/default)
- Disabled state support
- Label positioning (left/top)
- Translation support via ngx-translate
- Two-way data binding with model signals

## Usage

### Basic Example - Button Style (Default)

```typescript
// In your component
export class MyComponent {
  selectedValue = signal<string>("option1");

  options = [
    { label: "Option 1", value: "option1" },
    { label: "Option 2", value: "option2" },
    { label: "Option 3", value: "option3" },
  ];

  onSelectionChange(value: any) {
    console.log("Selected:", value);
  }
}
```

```html
<!-- Button style (default) -->
<app-toggle-radio
  [label]="'Select an option'"
  [optionList]="options"
  [radioType]="'button'"
  [(value)]="selectedValue"
  (onSelected)="onSelectionChange($event)"
>
</app-toggle-radio>
```

### Circular Radio Style

```html
<!-- Circular radio style -->
<app-toggle-radio
  [label]="'Select an option'"
  [optionList]="options"
  [radioType]="'radio'"
  [(value)]="selectedValue"
  (onSelected)="onSelectionChange($event)"
>
</app-toggle-radio>
```

### Advanced Example

```html
<app-toggle-radio
  [label]="'Choose billing period'"
  [optionList]="billingOptions"
  [configKey]="{ label: 'name', value: 'id' }"
  [buttonStyle]="'solid'"
  [size]="'large'"
  [labelPosition]="'top'"
  [disabled]="false"
  [(value)]="selectedBilling"
  (onSelected)="onBillingChange($event)"
>
</app-toggle-radio>
```

### With Custom Configuration Keys

```typescript
// When your option objects have different property names
customOptions = [
  { name: "Monthly", id: "monthly" },
  { name: "Yearly", id: "yearly" },
];
```

```html
<app-toggle-radio
  [optionList]="customOptions"
  [configKey]="{ label: 'name', value: 'id' }"
  [(value)]="selectedOption"
>
</app-toggle-radio>
```

## API

### Inputs

| Property        | Type                               | Default                              | Description                                      |
| --------------- | ---------------------------------- | ------------------------------------ | ------------------------------------------------ |
| `label`         | `string`                           | `undefined`                          | Label text for the radio group                   |
| `optionList`    | `{ label: string; value: any }[]`  | `[]`                                 | Array of options to display                      |
| `configKey`     | `{ label: string; value: string }` | `{ label: "label", value: "value" }` | Configuration for option object keys             |
| `radioType`     | `"button" \| "radio"`              | `"button"`                           | Type of radio display (button style or circular) |
| `buttonStyle`   | `"outline" \| "solid"`             | `"outline"`                          | Style of radio buttons (only for button type)    |
| `size`          | `"large" \| "small" \| "default"`  | `"default"`                          | Size of radio buttons                            |
| `disabled`      | `boolean`                          | `false`                              | Whether the radio group is disabled              |
| `labelPosition` | `"left" \| "top"`                  | `"left"`                             | Position of the label                            |

### Model

| Property | Type         | Description                            |
| -------- | ------------ | -------------------------------------- |
| `value`  | `model<any>` | Two-way binding for the selected value |

### Outputs

| Event        | Type          | Description                    |
| ------------ | ------------- | ------------------------------ |
| `onSelected` | `output<any>` | Emitted when selection changes |

## Styling

The component includes basic styling for layout and positioning. You can override styles by targeting the component's CSS classes:

- `.container` - Main container
- `.radio-label` - Label styling

## Translation

The component supports translation through ngx-translate. Both the label and option labels will be automatically translated if translation keys are provided.

## Examples

### Example 1: Simple A, B, C, D Options (Circular Style)

```typescript
export class ExampleComponent {
  selectedOption = signal<string>("A");

  options = [
    { label: "A", value: "A" },
    { label: "B", value: "B" },
    { label: "C", value: "C" },
    { label: "D", value: "D" },
  ];
}
```

```html
<app-toggle-radio
  [optionList]="options"
  [radioType]="'radio'"
  [(value)]="selectedOption"
>
</app-toggle-radio>
```

### Example 2: Billing Period Selection (Button Style)

```typescript
export class BillingComponent {
  selectedBilling = signal<string>("monthly");

  billingOptions = [
    { label: "Monthly", value: "monthly" },
    { label: "Yearly", value: "yearly" },
  ];
}
```

```html
<app-toggle-radio
  [label]="'Billing Period'"
  [optionList]="billingOptions"
  [radioType]="'button'"
  [buttonStyle]="'solid'"
  [(value)]="selectedBilling"
>
</app-toggle-radio>
```
