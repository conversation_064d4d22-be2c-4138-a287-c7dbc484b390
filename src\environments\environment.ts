import {
  languageCodeType,
  languageCountryCodeType,
} from '@core/services/utils/language';
import { roleType } from '@models/enums/role';
import { ISettings } from '@models/interfaces/configuration';

export const environment: ISettings = {
  production: `${process.env['PRODUCTION']}` === 'true',
  api: {
    base: `${process.env['BASE_API_URL']}`,
    auth: `${process.env['BASE_API_URL']}/api/v1/auth/admin`,
    admin: `${process.env['BASE_API_URL']}/api/v1/admin`,
    notifications: `${process.env['BASE_API_URL']}/api/v1/notifications`,
  },
  languages: [
    {
      name: 'Italiano',
      code: languageCodeType.it,
      countryCode: languageCountryCodeType.IT,
      enabled: true,
      default: true,
    },
    {
      name: 'English',
      code: languageCodeType.en,
      countryCode: languageCountryCodeType.GB,
      enabled: true,
    },
  ],
  sections: [
    {
      level: 0,
      title: 'admin',
      id: 'M_6',
      icon: 'biuti-ui:admin',
      rolePermission: [roleType.admin],
      routerLink: 'admin',
    },
  ],
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
