html {
  &.light {

    .ant-menu-light .ant-menu-item:hover,
    .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
    .ant-menu-light .ant-menu-submenu-title:hover {
      background: #f7f7f7;
    }
  }
}

html {
  &.dark {

    .ant-menu-light .ant-menu-item:hover,
    .ant-menu-light .ant-menu-item-active,
    .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
    .ant-menu-light .ant-menu-submenu-active,
    .ant-menu-light .ant-menu-submenu-title:hover {
      background: black;
    }

    .ant-menu-sub.ant-menu-inline {
      background: black;
    }
  }
}


.ant-table-pagination.ant-pagination {
  margin-bottom: 0 !important;
}

.ant-list-item-action>li:first-child {
  padding-right: 0;
  margin-right: -8px;
}

// input.ant-input-number-input {
//   text-align: end;
//   width: calc(100% - 2.5rem)
// }

nz-list-item-meta-title>.ant-list-item-meta-title {
  margin-bottom: -2px;
}

.ant-upload-list {
  // display: flex;
  display: inline-flex;
  flex-wrap: wrap;
}

nz-tag.ant-tag {
  margin-right: 0;
}

.ant-collapse>.ant-collapse-item>.ant-collapse-header {
  align-items: center !important
}

.ant-menu.ant-menu-inline-collapsed>.ant-menu-item,
.ant-menu.ant-menu-inline-collapsed>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item,
.ant-menu.ant-menu-inline-collapsed>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title,
.ant-menu.ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title {
  left: 0;
  padding: 0 calc(50% - 32px / 2) !important;
  text-overflow: clip;
  /* margin: 0 8px; */
  margin-left: 8px;
  margin-right: 8px;
  border-radius: 6px;
}


.ant-menu-vertical .ant-menu-item:not(:last-child),
.ant-menu-vertical-left .ant-menu-item:not(:last-child),
.ant-menu-vertical-right .ant-menu-item:not(:last-child),
.ant-menu-inline .ant-menu-item {
  margin-bottom: 8px;
  border-radius: 6px;
  /* padding: 0 16px; */
  margin-top: 8px;
  margin-left: 8px;
}

.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  width: calc(100% - 16px) !important;
}

.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover,
.ant-dropdown-menu-item.ant-dropdown-menu-item-active,
.ant-dropdown-menu-item.ant-dropdown-menu-submenu-title-active,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-item-active,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-submenu-title-active {
  border-radius: 6px;
}

.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu-title {
  border-radius: 6px;
  margin-left: 4px !important;
  margin-right: 4px !important;
}

.ant-dropdown-menu-item:not(:first-child) {
  margin-top: 4px;
}

.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-item-active,
.ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
.ant-menu-light .ant-menu-submenu-active,
.ant-menu-light .ant-menu-submenu-title:hover {
  border-radius: 6px;
}

.ant-menu-vertical .ant-menu-submenu,
.ant-menu-vertical-left .ant-menu-submenu,
.ant-menu-vertical-right .ant-menu-submenu,
.ant-menu-inline .ant-menu-submenu {
  border-radius: 6px;
  margin-top: 8px;
  margin-left: 8px;
}

.ant-table-filter-dropdown {

  ol,
  ul,
  dl {
    margin-top: 0.3em;
    margin-bottom: 0.3em;
  }
}

.ant-menu-vertical .ant-menu-item::after,
.ant-menu-vertical-left .ant-menu-item::after,
.ant-menu-vertical-right .ant-menu-item::after,
.ant-menu-inline .ant-menu-item::after {
  border: none !important;
}

.ant-menu-inline .ant-menu-item:not(:last-child) {
  margin-bottom: 4px !important;
}

// html.light .ant-menu-light .ant-menu-item:hover,
// html.light .ant-menu-light .ant-menu-item-active,
// html.light .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
// html.light .ant-menu-light .ant-menu-submenu-active,
// html.light .ant-menu-light .ant-menu-submenu-title:hover {}

//@media screen and (max-width: 768px) {
//  .ant-menu-inline .ant-menu-item, .ant-menu-inline .ant-menu-submenu-title {
//    width: calc(100% - 32px) !important;
//  }
//}