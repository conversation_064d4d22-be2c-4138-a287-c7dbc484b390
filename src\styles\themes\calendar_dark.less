.calendar-container .calendar-header {
  background: @dark !important;
}

.cal-week-view .cal-header.cal-weekend span {
  color: #ff4646;
  font-weight: bold;
}

mwl-calendar-week-view-header .cal-day-headers {
  background: @primary-color;

  :hover {
    background: @base-blue-hover !important;
  }

  .cal-header.cal-today {
    background: @red;
  }
}

mwl-calendar-week-view-hour-segment {
  background: @dark;

  :hover {
    background: @base-blue-hover !important;
  }
}

.cal-week-view .cal-day-column {
  border-left-color: @primary-color !important;
}

.cal-week-view .cal-hour:not(:last-child) .cal-hour-segment,
.cal-week-view .cal-hour:last-child :not(:last-child) .cal-hour-segment {
  border-bottom-color: @primary-color !important;
  border-bottom: solid 1px;
}

.cal-week-view .cal-time-label-column .cal-hour-segment-content,
.cal-day-view .cal-time-label-column .cal-hour-segment-content {
  background: @primary-color;
}

.cal-week-view .cal-day-cell {
  background: @dark;
}

mwl-calendar-month-view-header .cal-header {
  background: @primary-color;

  :hover {
    background: @base-blue-hover !important;
  }
}

.cal-month-view .cal-header .cal-cell {
  :hover {
    background: @base-blue-hover !important;
  }
}

.cal-month-view.cal-day-cell {
  .cal-cell-top {
    .cal-day-badge {
      :hover {
        background-color: @cyan !important;
      }
    }
  }

}

.cal-month-view .cal-day-cell.cal-today {
  background: @primary-color !important;
}

.cal-month-view .cal-cell-row:hover {
  background: @primary-color;
}

mwl-calendar-month-cell {
  ::selection {
    color: unset;
  }

  :hover {
    background: @base-blue-hover !important;
  }
}


.cal-month-view .cal-day-cell {
  background: @dark;
}

.day-view-container {
  .custom-time-column {
    width: 80px;
    background-color: @day_time_slot_bg;
    border: 1px solid @white;
    border-right-color: black;
    border-style: solid !important;
    text-align: center;
    z-index: 2;
  }

  .time-slot {
    &:not(:last-child) {
      border-bottom: 1px solid @black !important;
    }
  }
}

.cal-month-view .cal-open-day-events {
  color: @white;
  background-color: @dark;
  -webkit-box-shadow: unset;
  box-shadow: unset;
  border: 1px solid @extra_light_grey;
}

.calendar-working-hours {
  background-image: linear-gradient(135deg, rgb(68, 68, 68) 10%, rgb(31, 31, 44) 10%, rgb(31, 31, 44) 50%, rgb(68, 68, 68) 50%, rgb(68, 68, 68) 60%, rgb(31, 31, 44) 60%, rgb(31, 31, 44) 100%);
  background-size: 7px 7px;
  border-radius: 4px;
}