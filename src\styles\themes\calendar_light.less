.calendar-container .calendar-header {
  background: @white !important;
}

.day-view-container {
  .custom-time-column {
    width: 80px;
    background-color: @day_time_slot_bg;
    border: 1px solid #ddd !important;
    border-right-color: black;
    text-align: center;
  }

  .time-slot {
    &:not(:last-child) {
      border-bottom: 1px dashed rgba(82, 82, 82, 0.68) !important;
    }

  }
}

mwl-calendar-week-view-hour-segment {
  background: @white;

  :hover {
    // background: @base-blue-hover !important;
  }
}

.cal-month-view .cal-open-day-events {
  color: unset;
  background-color: unset;
  -webkit-box-shadow: unset;
  box-shadow: unset;
  border: 1px solid @extra_light_grey;
}

.calendar-closed-day {
  background-image: linear-gradient(135deg, rgb(170, 170, 170) 10%, rgb(200, 200, 200) 10%, rgb(200, 200, 200) 50%, rgb(170, 170, 170) 50%, rgb(170, 170, 170) 60%, rgb(200, 200, 200) 60%, rgb(200, 200, 200) 100%);
  background-size: 7px 7px;
}

.calendar-working-hours {
  background-image: linear-gradient(135deg, rgb(170, 170, 170) 10%, rgb(200, 200, 200) 10%, rgb(200, 200, 200) 50%, rgb(170, 170, 170) 50%, rgb(170, 170, 170) 60%, rgb(200, 200, 200) 60%, rgb(200, 200, 200) 100%);
  background-size: 7px 7px;
  border-radius: 4px;
}