@import (multiple) "../../../node_modules/ng-zorro-antd/src/style/themes/dark";
@import (multiple) "./base";

//Main
@primary-color: @base-blue;
@secondary-color: @cyan;
@accent-color: @blue-marin;
@border-radius-base: 6px;
@input-affix-margin: 6px;
@text-color-primary: @white;
@text-color-secondary: @medium_grey;

//Calendar
@header-background: @component-background;
@header-top-border: @white;
@day_time_slot_bg: @primary-color;
@day_time_slot_color: @white;


//Footer
@footer-text-opacity: rgba(255, 255, 255, 0.548);
@footer-text-light-opacity: rgba(255, 255, 255, 0.705);

//List
@list-header-background: @dark;
@list-footer-background: @dark;

//hover
@hover-color: @black;

//Page Header
@top-bar-actions-hover: @hover-color;
@circle-radius: @black;
@triangle-radius: @dark;
@avatar-bg: @primary-color;
//
@component-bg: @dark;
@dropdown-menu-bg: @dark;
@sider-menu: @dark;
@border-color-base: #4f4f4f;
@border-color: #303030;

//Layout
@layout-sider-background: @dark;
@layout-header-background: @dark;
@layout-main-page: @dark;

// Typography //
@typography-title-margin-top: 0;
@typography-title-margin-bottom: 0;

//Spin
@spin-bg: #141414;

//Hours
@hours-title: @extra_light_grey;
@hours-subtitle: @medium_grey;

//Scrollbar
@scrollbar-color: #434343 #262626;

//Subscription
@subscription-plan-text: @white;
@subscription-price-text: @white;
@subscription-plan-description-text: @medium_grey;
@subscription-feature-text: @light_grey;
@subscription-secondary-text: @medium_grey;
@subscription-border: 1px solid @strong_grey;


// Customers
@customer-text: @light_grey;


// Dashboard
@dashboard-card-bg1: rgba(54, 107, 249, 0.2);
@dashboard-card-bg2: rgba(15, 179, 138, 0.2);
@dashboard-card-bg3: rgba(237, 81, 31, 0.2);
@dashboard-icon-color: @light_grey;
@dashboard-title-color: @white;
@dashboard-subtitle-color: @light_grey;
@dashboard-bookings-card-description: @medium_grey;
@dashboard-date: @medium_grey;

// Sidebar
@info-box-bg: @ultra_strong_gray;

// Salon
@salon-image-bg: @ultra_strong_gray;