@import (multiple) "../../../node_modules/ng-zorro-antd/src/style/themes/default";
@import (multiple) "./base";

//Main
@primary-color: @base-blue;
@secondary-color: @cyan;
@accent-color: @blue-marin;
@border-radius-base: 6px;
@input-affix-margin: 6px;
@text-color-primary: @black;
@text-color-secondary: @strong_grey;

//Calendar
@header-background: @white;
@header-top-border: @light_grey;
@day_time_slot_bg: @white;
@day_time_slot_color: @black;

//Footer
@footer-text-opacity: rgba(0, 0, 0, 0.548);
@footer-text-light-opacity: rgba(0, 0, 0, 0.705);

//List
@list-header-background: @white;
@list-footer-background: @white;

//hover
@hover-color: #f7f7f7;

//Page Header
@top-bar-actions-hover: @hover-color;
@circle-radius: #f0f2f5;
@triangle-radius: @component-bg;
@avatar-bg: @primary-color;
//
@component-bg: @extra_light_grey;
@dropdown-menu-bg: @white;
@sider-menu: @white;
// @border-color-base: #e7e7e7;

//Layout
@layout-sider-background: @white;
@layout-header-background: @white;
@layout-main-page: @white;
@menu-inline-submenu-bg: @white;

@box-inset: inset 0 1.5rem 3.75rem rgba(0, 0, 0, 0.1);

// Typography //
@typography-title-margin-top: 0;
@typography-title-margin-bottom: 0;

//Spin
@spin-bg: #fdfdfd;

//Hours
@hours-title: @dark;
@hours-subtitle: @extra_strong_grey;

//Scrollbar
@scrollbar-color: inherit;

//Subscription
@subscription-plan-text: @dark;
@subscription-plan-description-text: @extra_strong_grey;
@subscription-price-text: @dark;
@subscription-feature-text: @dark;
@subscription-secondary-text: @extra_strong_grey;
@subscription-border: 1px solid @border-color-base;

// Customers
@customer-text: @dark;

// Dashboard
@dashboard-card-bg1: #366BF9;
@dashboard-card-bg2: #0fb38a;
@dashboard-card-bg3: #ED511F;
@dashboard-icon-color: @white;
@dashboard-title-color: @white;
@dashboard-subtitle-color: @white;
@dashboard-bookings-card-description: @strong_grey;
@dashboard-date: @strong_grey;

// Sidebar
@info-box-bg: @light_grey;

// Salon
@salon-image-bg: @extra_light_grey;